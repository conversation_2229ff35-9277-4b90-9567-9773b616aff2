<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page isErrorPage="true" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统错误 - 内江移动支撑项目平台</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #ffa726 0%, #ff7043 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .error-code {
            font-size: 80px;
            font-weight: bold;
            color: #ffa726;
            margin: 0;
            line-height: 1;
        }
        .error-title {
            font-size: 24px;
            color: #333;
            margin: 20px 0 10px 0;
        }
        .error-message {
            color: #666;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
        }
        .btn-primary {
            background: #ffa726;
            color: white;
        }
        .btn-primary:hover {
            background: #ff9800;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
        }
        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        .error-icon {
            font-size: 60px;
            margin-bottom: 20px;
        }
        .footer-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 12px;
        }
        .error-details {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">🚨</div>
        <h1 class="error-code">ERROR</h1>
        <h2 class="error-title">系统错误</h2>
        <p class="error-message">
            系统遇到了一个意外错误，请稍后重试。<br>
            如果问题持续存在，请联系系统管理员。
        </p>
        
        <% if (exception != null) { %>
        <div class="error-details">
            <strong>错误类型：</strong> <%= exception.getClass().getSimpleName() %><br>
            <% if (exception.getMessage() != null) { %>
            <strong>错误信息：</strong> <%= exception.getMessage() %>
            <% } %>
        </div>
        <% } %>
        
        <div class="btn-group">
            <a href="<%= request.getContextPath() %>/" class="btn btn-primary">返回首页</a>
            <a href="javascript:location.reload()" class="btn btn-secondary">重新加载</a>
            <a href="<%= request.getContextPath() %>/user/logout" class="btn btn-secondary">重新登录</a>
        </div>
        
        <div class="footer-info">
            <p>内江移动支撑项目平台系统</p>
            <p>错误时间: <%= new java.util.Date() %></p>
            <p>请保存此页面信息并联系技术支持</p>
        </div>
    </div>
</body>
</html>
