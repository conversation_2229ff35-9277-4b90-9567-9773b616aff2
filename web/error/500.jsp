<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page isErrorPage="true" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器错误 - 内江移动支撑项目平台</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            padding: 40px;
            text-align: center;
            max-width: 500px;
            width: 90%;
        }
        .error-code {
            font-size: 120px;
            font-weight: bold;
            color: #ff6b6b;
            margin: 0;
            line-height: 1;
        }
        .error-title {
            font-size: 24px;
            color: #333;
            margin: 20px 0 10px 0;
        }
        .error-message {
            color: #666;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-block;
        }
        .btn-primary {
            background: #ff6b6b;
            color: white;
        }
        .btn-primary:hover {
            background: #ff5252;
            transform: translateY(-2px);
        }
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
        }
        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        .error-icon {
            font-size: 60px;
            margin-bottom: 20px;
        }
        .footer-info {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 12px;
        }
        .error-details {
            background: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
            font-size: 12px;
            color: #666;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <h1 class="error-code">500</h1>
        <h2 class="error-title">服务器内部错误</h2>
        <p class="error-message">
            抱歉，服务器遇到了一个内部错误，无法完成您的请求。<br>
            我们的技术团队已经收到通知，正在处理这个问题。
        </p>
        
        <% if (exception != null && request.getParameter("debug") != null) { %>
        <div class="error-details">
            <strong>错误详情：</strong><br>
            <%= exception.getClass().getSimpleName() %>: <%= exception.getMessage() %><br>
            <% 
                java.io.StringWriter sw = new java.io.StringWriter();
                java.io.PrintWriter pw = new java.io.PrintWriter(sw);
                exception.printStackTrace(pw);
                String stackTrace = sw.toString();
                if (stackTrace.length() > 1000) {
                    stackTrace = stackTrace.substring(0, 1000) + "...";
                }
            %>
            <pre style="font-size: 10px; margin-top: 10px;"><%= stackTrace %></pre>
        </div>
        <% } %>
        
        <div class="btn-group">
            <a href="<%= request.getContextPath() %>/" class="btn btn-primary">返回首页</a>
            <a href="javascript:location.reload()" class="btn btn-secondary">重新加载</a>
            <a href="javascript:history.back()" class="btn btn-secondary">返回上页</a>
        </div>
        
        <div class="footer-info">
            <p>内江移动支撑项目平台系统</p>
            <p>错误时间: <%= new java.util.Date() %></p>
            <p>如果问题持续存在，请联系系统管理员</p>
        </div>
    </div>
</body>
</html>
