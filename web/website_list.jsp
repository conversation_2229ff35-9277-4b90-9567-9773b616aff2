<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>12580网站导航 - 内江移动支撑项目平台系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header h1 {
            font-size: 1.8rem;
        }
        
        .nav-links a {
            color: white;
            text-decoration: none;
            margin-left: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            transition: background 0.3s ease;
        }
        
        .nav-links a:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 3rem;
            color: white;
        }
        
        .page-header h2 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }
        
        .page-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .category-filter {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .category-filter h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        .category-buttons {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 1rem;
        }
        
        .category-btn {
            padding: 0.8rem 1.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .category-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .category-btn.active {
            background: linear-gradient(45deg, #f093fb, #f5576c);
        }
        
        .websites-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 2rem;
        }
        
        .website-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }
        
        .website-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        
        .website-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
        
        .website-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .website-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .website-category {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        
        .website-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1.5rem;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .website-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
            color: #888;
        }
        
        .visit-count {
            display: flex;
            align-items: center;
            gap: 0.3rem;
        }
        
        .website-actions {
            display: flex;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.8rem 1.5rem;
            border: none;
            border-radius: 25px;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(45deg, #f093fb, #f5576c);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .status-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .status-normal {
            background: #d4edda;
            color: #155724;
        }
        
        .status-maintenance {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-disabled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: white;
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.7;
        }
        
        .search-box {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .search-box input {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.8);
        }
        
        .search-box input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>12580网站导航</h1>
        <div class="nav-links">
            <a href="${pageContext.request.contextPath}/index.jsp">首页</a>
            <c:if test="${not empty sessionScope.user}">
                <a href="${pageContext.request.contextPath}/website/list">管理</a>
                <a href="${pageContext.request.contextPath}/user/logout">退出</a>
            </c:if>
            <c:if test="${empty sessionScope.user}">
                <a href="${pageContext.request.contextPath}/login.jsp">登录</a>
            </c:if>
        </div>
    </div>

    <div class="container">
        <div class="page-header">
            <h2>12580网站服务平台</h2>
            <p>为您提供便民服务、生活信息、商务查询等综合信息服务</p>
        </div>

        <div class="search-box">
            <input type="text" id="searchInput" placeholder="搜索网站名称、描述或关键词..." onkeyup="searchWebsites()">
        </div>

        <div class="category-filter">
            <h3>网站分类</h3>
            <div class="category-buttons">
                <a href="${pageContext.request.contextPath}/website/category" 
                   class="category-btn ${empty currentCategory ? 'active' : ''}">全部</a>
                <a href="${pageContext.request.contextPath}/website/category?cat=信息服务" 
                   class="category-btn ${'信息服务' eq currentCategory ? 'active' : ''}">信息服务</a>
                <a href="${pageContext.request.contextPath}/website/category?cat=官方网站" 
                   class="category-btn ${'官方网站' eq currentCategory ? 'active' : ''}">官方网站</a>
                <a href="${pageContext.request.contextPath}/website/category?cat=在线服务" 
                   class="category-btn ${'在线服务' eq currentCategory ? 'active' : ''}">在线服务</a>
                <a href="${pageContext.request.contextPath}/website/category?cat=电商平台" 
                   class="category-btn ${'电商平台' eq currentCategory ? 'active' : ''}">电商平台</a>
                <a href="${pageContext.request.contextPath}/website/category?cat=娱乐服务" 
                   class="category-btn ${'娱乐服务' eq currentCategory ? 'active' : ''}">娱乐服务</a>
            </div>
        </div>

        <div class="websites-grid" id="websitesGrid">
            <c:choose>
                <c:when test="${not empty websites}">
                    <c:forEach var="website" items="${websites}">
                        <div class="website-card" data-name="${website.siteName}" data-description="${website.description}" data-keywords="${website.keywords}">
                            <div class="status-badge ${website.status == 1 ? 'status-normal' : (website.status == 2 ? 'status-maintenance' : 'status-disabled')}">
                                ${website.status == 1 ? '正常' : (website.status == 2 ? '维护中' : '已停用')}
                            </div>
                            
                            <div class="website-header">
                                <div>
                                    <div class="website-title">${website.siteName}</div>
                                    <div class="website-category">${website.category}</div>
                                </div>
                            </div>
                            
                            <div class="website-description">
                                ${website.description}
                            </div>
                            
                            <div class="website-meta">
                                <div class="visit-count">
                                    <span>👁</span>
                                    <span>${website.visitCount} 次访问</span>
                                </div>
                                <div>
                                    <fmt:formatDate value="${website.createTime}" pattern="yyyy-MM-dd"/>
                                </div>
                            </div>
                            
                            <div class="website-actions">
                                <c:if test="${website.status == 1}">
                                    <a href="${pageContext.request.contextPath}/website/visit?id=${website.websiteId}" 
                                       class="btn btn-primary" target="_blank">
                                        🌐 访问网站
                                    </a>
                                </c:if>
                                <c:if test="${website.status != 1}">
                                    <span class="btn" style="background: #ccc; cursor: not-allowed;">
                                        🚫 暂不可用
                                    </span>
                                </c:if>
                                <a href="${pageContext.request.contextPath}/website/view?id=${website.websiteId}" 
                                   class="btn btn-secondary">
                                    📋 详情
                                </a>
                            </div>
                        </div>
                    </c:forEach>
                </c:when>
                <c:otherwise>
                    <div class="empty-state">
                        <i>🌐</i>
                        <h3>暂无网站数据</h3>
                        <p>当前分类下没有可用的网站</p>
                    </div>
                </c:otherwise>
            </c:choose>
        </div>
    </div>

    <script>
        function searchWebsites() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toLowerCase();
            const cards = document.querySelectorAll('.website-card');

            cards.forEach(card => {
                const name = card.getAttribute('data-name').toLowerCase();
                const description = card.getAttribute('data-description').toLowerCase();
                const keywords = card.getAttribute('data-keywords') ? card.getAttribute('data-keywords').toLowerCase() : '';
                
                const found = name.indexOf(filter) > -1 || 
                             description.indexOf(filter) > -1 || 
                             keywords.indexOf(filter) > -1;
                
                card.style.display = found ? 'block' : 'none';
            });
        }

        // 卡片动画效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.website-card');
            
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
