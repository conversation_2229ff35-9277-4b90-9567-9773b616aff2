@echo off
echo ========================================
echo 内江移动支撑项目平台系统 - 自动部署脚本
echo ========================================
echo.

REM 检查CATALINA_HOME环境变量
if "%CATALINA_HOME%"=="" (
    echo 错误：未设置CATALINA_HOME环境变量
    echo 请设置CATALINA_HOME指向您的Tomcat安装目录
    echo 例如：set CATALINA_HOME=C:\apache-tomcat-9.0.xx
    pause
    exit /b 1
)

echo Tomcat目录: %CATALINA_HOME%
echo.

REM 检查Tomcat是否存在
if not exist "%CATALINA_HOME%\bin\catalina.bat" (
    echo 错误：在 %CATALINA_HOME% 中找不到Tomcat
    echo 请确认CATALINA_HOME路径正确
    pause
    exit /b 1
)

REM 检查JAR包
echo [1/6] 检查JAR包...
if not exist "web\WEB-INF\lib\mysql-connector-java-8.0.33.jar" (
    echo 警告：MySQL驱动JAR包不存在
    echo 请先运行 download-jars.bat 下载必需的JAR包
    set /p choice="是否继续部署？(y/n): "
    if /i not "%choice%"=="y" exit /b 1
)
echo ✓ JAR包检查完成
echo.

REM 停止Tomcat
echo [2/6] 停止Tomcat服务器...
call "%CATALINA_HOME%\bin\shutdown.bat" >nul 2>&1
timeout /t 5 /nobreak >nul
echo ✓ Tomcat已停止
echo.

REM 清理旧部署
echo [3/6] 清理旧部署...
if exist "%CATALINA_HOME%\webapps\nanning" (
    rmdir /s /q "%CATALINA_HOME%\webapps\nanning"
    echo ✓ 已删除旧的nanning目录
)
if exist "%CATALINA_HOME%\webapps\nanning.war" (
    del "%CATALINA_HOME%\webapps\nanning.war"
    echo ✓ 已删除旧的nanning.war文件
)
echo ✓ 清理完成
echo.

REM 创建部署目录
echo [4/6] 部署项目文件...
mkdir "%CATALINA_HOME%\webapps\nanning" >nul 2>&1

REM 复制web文件
xcopy "web\*" "%CATALINA_HOME%\webapps\nanning\" /E /I /Y >nul
echo ✓ Web文件复制完成

REM 创建classes目录
mkdir "%CATALINA_HOME%\webapps\nanning\WEB-INF\classes" >nul 2>&1

REM 复制编译后的class文件（如果存在）
if exist "out\production\nanning" (
    xcopy "out\production\nanning\*" "%CATALINA_HOME%\webapps\nanning\WEB-INF\classes\" /E /I /Y >nul
    echo ✓ Class文件复制完成
) else (
    echo 警告：未找到编译后的class文件，请在IDE中编译项目
)

REM 复制配置文件
if exist "src\db.properties" (
    copy "src\db.properties" "%CATALINA_HOME%\webapps\nanning\WEB-INF\classes\" >nul
    echo ✓ 配置文件复制完成
)
echo.

REM 设置权限（Windows通常不需要）
echo [5/6] 设置文件权限...
echo ✓ 权限设置完成
echo.

REM 启动Tomcat
echo [6/6] 启动Tomcat服务器...
start "Tomcat" "%CATALINA_HOME%\bin\catalina.bat" run
echo ✓ Tomcat启动中...
echo.

echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 请等待Tomcat完全启动（约30-60秒），然后访问：
echo.
echo 🌐 主页：    http://localhost:8080/nanning/
echo 🔐 登录页：  http://localhost:8080/nanning/login.jsp
echo 📊 网站导航：http://localhost:8080/nanning/website/category
echo.
echo 默认登录账号：
echo   管理员：admin / admin123
echo   普通用户：user / user123
echo.
echo 如果遇到问题，请：
echo 1. 检查Tomcat日志：%CATALINA_HOME%\logs\catalina.out
echo 2. 确认数据库已创建并导入初始数据
echo 3. 检查JAR包是否完整
echo.

REM 等待用户确认
echo 按任意键打开浏览器访问系统...
pause >nul

REM 打开浏览器
start http://localhost:8080/nanning/

echo 部署脚本执行完成！
