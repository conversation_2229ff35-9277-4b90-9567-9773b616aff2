# 内江移动支撑项目平台系统

## 项目简介

内江移动支撑项目平台系统是一个基于Java Web技术栈开发的综合管理平台，主要提供12580网站管理、用户管理、组织管理等功能模块。

## 功能特性

### 🌐 12580网站管理
- 网站信息管理（增删改查）
- 网站分类管理
- 访问统计
- 网站状态管理（正常/维护/停用）
- 网站导航展示

### 👥 用户管理
- 用户注册/登录
- 用户信息管理
- 角色权限控制
- 密码加密存储
- 用户状态管理

### 🏢 组织管理
- 组织架构管理
- 层级关系维护
- 组织信息管理

### 📊 系统管理
- 系统日志记录
- 数据统计分析
- 权限控制

## 技术栈

- **后端**: Java 8+, Servlet, JSP
- **数据库**: MySQL 8.0
- **前端**: HTML5, CSS3, JavaScript
- **服务器**: Tomcat 9.0+
- **开发工具**: IntelliJ IDEA

## 项目结构

```
nanning/
├── src/                          # 源代码目录
│   ├── com/neijiang/mobile/
│   │   ├── model/               # 实体类
│   │   │   ├── User.java
│   │   │   ├── Website.java
│   │   │   └── Organization.java
│   │   ├── dao/                 # 数据访问层
│   │   │   ├── UserDao.java
│   │   │   └── WebsiteDao.java
│   │   ├── service/             # 业务逻辑层
│   │   │   ├── UserService.java
│   │   │   └── WebsiteService.java
│   │   ├── servlet/             # 控制器
│   │   │   ├── UserServlet.java
│   │   │   └── WebsiteServlet.java
│   │   ├── filter/              # 过滤器
│   │   │   ├── CharacterEncodingFilter.java
│   │   │   └── LoginFilter.java
│   │   └── util/                # 工具类
│   │       └── DBUtil.java
│   ├── db.properties            # 数据库配置
│   └── Main.java               # 主类
├── web/                         # Web资源目录
│   ├── WEB-INF/
│   │   └── web.xml             # Web配置文件
│   ├── admin/                  # 管理页面
│   │   └── user_list.jsp
│   ├── index.jsp               # 首页
│   ├── login.jsp               # 登录页面
│   └── website_list.jsp        # 网站列表页面
├── sql/                        # 数据库脚本
│   └── init.sql               # 初始化脚本
└── README.md                   # 项目说明
```

## 安装部署

### 1. 环境要求
- JDK 8 或更高版本
- MySQL 8.0 或更高版本
- Tomcat 9.0 或更高版本
- IntelliJ IDEA（推荐）

### 2. 数据库配置
1. 创建MySQL数据库
2. 执行 `sql/init.sql` 脚本初始化数据库
3. 修改 `src/db.properties` 中的数据库连接信息

### 3. 项目配置
1. 在IntelliJ IDEA中打开项目
2. 配置Tomcat服务器
3. 添加MySQL JDBC驱动到项目依赖
4. 配置项目部署路径

### 4. 启动项目
1. 启动MySQL数据库服务
2. 启动Tomcat服务器
3. 访问 `http://localhost:8080/nanning`

## 默认账号

系统提供以下默认测试账号：

| 用户名 | 密码 | 角色 | 说明 |
|--------|------|------|------|
| admin | admin123 | 管理员 | 拥有所有权限 |
| user | user123 | 普通用户 | 基本权限 |

## 主要功能页面

### 首页
- 系统概览
- 功能模块导航
- 用户登录状态显示

### 用户管理
- `/user/list` - 用户列表
- `/user/add` - 添加用户
- `/user/edit` - 编辑用户
- `/login.jsp` - 用户登录

### 网站管理
- `/website/category` - 网站分类浏览
- `/website/list` - 网站管理列表
- `/website/add` - 添加网站
- `/website/stats` - 网站统计

## 开发说明

### 数据库设计
- `users` - 用户表
- `websites` - 网站表
- `organizations` - 组织表
- `roles` - 角色表
- `system_logs` - 系统日志表

### 权限控制
- 使用Filter实现登录验证
- 基于角色的权限控制
- 管理员和普通用户权限分离

### 安全特性
- 密码MD5加密
- SQL注入防护
- XSS攻击防护
- 会话管理

## API接口

### 用户相关
- `POST /user/login` - 用户登录
- `POST /user/register` - 用户注册
- `GET /user/list` - 获取用户列表
- `POST /user/add` - 添加用户
- `POST /user/update` - 更新用户

### 网站相关
- `GET /website/list` - 获取网站列表
- `GET /website/category` - 按分类获取网站
- `POST /website/add` - 添加网站
- `POST /website/update` - 更新网站
- `GET /website/visit` - 访问网站

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

- 项目地址: [GitHub Repository]
- 技术支持: 内江移动技术部
- 邮箱: <EMAIL>

## 更新日志

### v1.0.0 (2025-01-20)
- 初始版本发布
- 实现基础用户管理功能
- 实现12580网站管理功能
- 实现权限控制系统
- 完成基础UI界面设计

---

**内江移动支撑项目平台系统** - 为移动通信服务提供强有力的技术支撑！
