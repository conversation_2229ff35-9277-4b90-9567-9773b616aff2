#!/bin/bash

echo "========================================"
echo "内江移动支撑项目平台系统 - 自动部署脚本"
echo "========================================"
echo

# 检查CATALINA_HOME环境变量
if [ -z "$CATALINA_HOME" ]; then
    echo "错误：未设置CATALINA_HOME环境变量"
    echo "请设置CATALINA_HOME指向您的Tomcat安装目录"
    echo "例如：export CATALINA_HOME=/opt/apache-tomcat-9.0.xx"
    exit 1
fi

echo "Tomcat目录: $CATALINA_HOME"
echo

# 检查Tomcat是否存在
if [ ! -f "$CATALINA_HOME/bin/catalina.sh" ]; then
    echo "错误：在 $CATALINA_HOME 中找不到Tomcat"
    echo "请确认CATALINA_HOME路径正确"
    exit 1
fi

# 检查JAR包
echo "[1/6] 检查JAR包..."
if [ ! -f "web/WEB-INF/lib/mysql-connector-java-8.0.33.jar" ]; then
    echo "警告：MySQL驱动JAR包不存在"
    echo "请先运行 ./download-jars.sh 下载必需的JAR包"
    read -p "是否继续部署？(y/n): " choice
    if [ "$choice" != "y" ] && [ "$choice" != "Y" ]; then
        exit 1
    fi
fi
echo "✓ JAR包检查完成"
echo

# 停止Tomcat
echo "[2/6] 停止Tomcat服务器..."
$CATALINA_HOME/bin/shutdown.sh >/dev/null 2>&1
sleep 5
echo "✓ Tomcat已停止"
echo

# 清理旧部署
echo "[3/6] 清理旧部署..."
if [ -d "$CATALINA_HOME/webapps/nanning" ]; then
    rm -rf "$CATALINA_HOME/webapps/nanning"
    echo "✓ 已删除旧的nanning目录"
fi
if [ -f "$CATALINA_HOME/webapps/nanning.war" ]; then
    rm -f "$CATALINA_HOME/webapps/nanning.war"
    echo "✓ 已删除旧的nanning.war文件"
fi
echo "✓ 清理完成"
echo

# 创建部署目录
echo "[4/6] 部署项目文件..."
mkdir -p "$CATALINA_HOME/webapps/nanning"

# 复制web文件
cp -r web/* "$CATALINA_HOME/webapps/nanning/"
echo "✓ Web文件复制完成"

# 创建classes目录
mkdir -p "$CATALINA_HOME/webapps/nanning/WEB-INF/classes"

# 复制编译后的class文件（如果存在）
if [ -d "out/production/nanning" ]; then
    cp -r out/production/nanning/* "$CATALINA_HOME/webapps/nanning/WEB-INF/classes/"
    echo "✓ Class文件复制完成"
else
    echo "警告：未找到编译后的class文件，请在IDE中编译项目"
fi

# 复制配置文件
if [ -f "src/db.properties" ]; then
    cp "src/db.properties" "$CATALINA_HOME/webapps/nanning/WEB-INF/classes/"
    echo "✓ 配置文件复制完成"
fi
echo

# 设置权限
echo "[5/6] 设置文件权限..."
chmod -R 755 "$CATALINA_HOME/webapps/nanning"
echo "✓ 权限设置完成"
echo

# 启动Tomcat
echo "[6/6] 启动Tomcat服务器..."
$CATALINA_HOME/bin/startup.sh
echo "✓ Tomcat启动中..."
echo

echo "========================================"
echo "部署完成！"
echo "========================================"
echo
echo "请等待Tomcat完全启动（约30-60秒），然后访问："
echo
echo "🌐 主页：    http://localhost:8080/nanning/"
echo "🔐 登录页：  http://localhost:8080/nanning/login.jsp"
echo "📊 网站导航：http://localhost:8080/nanning/website/category"
echo
echo "默认登录账号："
echo "  管理员：admin / admin123"
echo "  普通用户：user / user123"
echo
echo "如果遇到问题，请："
echo "1. 检查Tomcat日志：tail -f $CATALINA_HOME/logs/catalina.out"
echo "2. 确认数据库已创建并导入初始数据"
echo "3. 检查JAR包是否完整"
echo

# 等待Tomcat启动
echo "等待Tomcat启动..."
sleep 10

# 检查Tomcat是否启动成功
if curl -s http://localhost:8080/nanning/ >/dev/null 2>&1; then
    echo "✓ 系统启动成功！"
    echo "🎉 您现在可以访问：http://localhost:8080/nanning/"
else
    echo "⚠️  系统可能还在启动中，请稍等片刻后访问"
fi

echo
echo "部署脚本执行完成！"
