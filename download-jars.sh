#!/bin/bash

echo "========================================"
echo "内江移动支撑项目平台系统 - JAR包下载脚本"
echo "========================================"
echo

# 创建lib目录
if [ ! -d "web/WEB-INF/lib" ]; then
    mkdir -p "web/WEB-INF/lib"
    echo "创建目录: web/WEB-INF/lib"
fi

cd web/WEB-INF/lib

echo "开始下载必需的JAR包..."
echo

# 下载MySQL驱动
echo "[1/6] 下载 MySQL Connector..."
if curl -L -o mysql-connector-java-8.0.33.jar "https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar"; then
    echo "✓ MySQL Connector 下载成功"
else
    echo "✗ MySQL Connector 下载失败"
fi
echo

# 下载JSTL
echo "[2/6] 下载 JSTL..."
if curl -L -o jstl-1.2.jar "https://repo1.maven.org/maven2/javax/servlet/jstl/1.2/jstl-1.2.jar"; then
    echo "✓ JSTL 下载成功"
else
    echo "✗ JSTL 下载失败"
fi
echo

# 下载Standard标签库
echo "[3/6] 下载 Standard 标签库..."
if curl -L -o standard-1.1.2.jar "https://repo1.maven.org/maven2/taglibs/standard/1.1.2/standard-1.1.2.jar"; then
    echo "✓ Standard 标签库下载成功"
else
    echo "✗ Standard 标签库下载失败"
fi
echo

# 下载Commons DBCP2
echo "[4/6] 下载 Commons DBCP2..."
if curl -L -o commons-dbcp2-2.9.0.jar "https://repo1.maven.org/maven2/org/apache/commons/commons-dbcp2/2.9.0/commons-dbcp2-2.9.0.jar"; then
    echo "✓ Commons DBCP2 下载成功"
else
    echo "✗ Commons DBCP2 下载失败"
fi
echo

# 下载Commons Pool2
echo "[5/6] 下载 Commons Pool2..."
if curl -L -o commons-pool2-2.11.1.jar "https://repo1.maven.org/maven2/org/apache/commons/commons-pool2/2.11.1/commons-pool2-2.11.1.jar"; then
    echo "✓ Commons Pool2 下载成功"
else
    echo "✗ Commons Pool2 下载失败"
fi
echo

# 下载Commons Logging
echo "[6/6] 下载 Commons Logging..."
if curl -L -o commons-logging-1.2.jar "https://repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar"; then
    echo "✓ Commons Logging 下载成功"
else
    echo "✗ Commons Logging 下载失败"
fi
echo

echo "========================================"
echo "下载完成！"
echo "========================================"
echo
echo "已下载的JAR包："
ls -la *.jar 2>/dev/null || echo "没有找到JAR包文件"
echo
echo "请确认所有JAR包都已成功下载，然后重启Tomcat服务器。"
echo
echo "如果某些JAR包下载失败，请手动从以下地址下载："
echo "1. MySQL Connector: https://dev.mysql.com/downloads/connector/j/"
echo "2. JSTL: https://mvnrepository.com/artifact/javax.servlet/jstl/1.2"
echo "3. Standard: https://mvnrepository.com/artifact/taglibs/standard/1.1.2"
echo "4. Commons DBCP2: https://mvnrepository.com/artifact/org.apache.commons/commons-dbcp2"
echo "5. Commons Pool2: https://mvnrepository.com/artifact/org.apache.commons/commons-pool2"
echo "6. Commons Logging: https://mvnrepository.com/artifact/commons-logging/commons-logging"
echo

cd ../../..

# 设置脚本可执行权限
chmod +x download-jars.sh

echo "脚本执行完成！"
