# 项目依赖JAR包说明

## 必需的JAR包列表

### 1. 数据库驱动
- **mysql-connector-java-8.0.33.jar**
  - 用途：MySQL数据库连接驱动
  - 版本：8.0.33 或更高
  - 下载地址：https://dev.mysql.com/downloads/connector/j/

### 2. Servlet API（通常由Tomcat提供）
- **servlet-api.jar**
  - 用途：Servlet和JSP开发API
  - 版本：4.0+
  - 说明：Tomcat自带，无需单独下载

### 3. JSP标准标签库（JSTL）
- **jstl-1.2.jar**
  - 用途：JSP标准标签库
  - 版本：1.2
  - 下载地址：https://mvnrepository.com/artifact/javax.servlet/jstl/1.2

- **standard-1.1.2.jar**
  - 用途：JSTL实现库
  - 版本：1.1.2
  - 下载地址：https://mvnrepository.com/artifact/taglibs/standard/1.1.2

### 4. 可选依赖（推荐）
- **commons-dbcp2-2.9.0.jar**
  - 用途：数据库连接池
  - 版本：2.9.0
  - 下载地址：https://mvnrepository.com/artifact/org.apache.commons/commons-dbcp2

- **commons-pool2-2.11.1.jar**
  - 用途：对象池（dbcp2依赖）
  - 版本：2.11.1
  - 下载地址：https://mvnrepository.com/artifact/org.apache.commons/commons-pool2

- **commons-logging-1.2.jar**
  - 用途：日志框架
  - 版本：1.2
  - 下载地址：https://mvnrepository.com/artifact/commons-logging/commons-logging

## JAR包放置位置

将下载的JAR包放置到以下目录：
```
web/WEB-INF/lib/
├── mysql-connector-java-8.0.33.jar
├── jstl-1.2.jar
├── standard-1.1.2.jar
├── commons-dbcp2-2.9.0.jar
├── commons-pool2-2.11.1.jar
└── commons-logging-1.2.jar
```

## 快速下载脚本

### Windows PowerShell脚本
```powershell
# 创建lib目录
New-Item -ItemType Directory -Force -Path "web\WEB-INF\lib"

# 下载JAR包（需要手动下载）
Write-Host "请手动下载以下JAR包到 web\WEB-INF\lib\ 目录："
Write-Host "1. mysql-connector-java-8.0.33.jar"
Write-Host "2. jstl-1.2.jar"
Write-Host "3. standard-1.1.2.jar"
Write-Host "4. commons-dbcp2-2.9.0.jar"
Write-Host "5. commons-pool2-2.11.1.jar"
Write-Host "6. commons-logging-1.2.jar"
```

### Linux/Mac Bash脚本
```bash
#!/bin/bash
# 创建lib目录
mkdir -p web/WEB-INF/lib

echo "请手动下载以下JAR包到 web/WEB-INF/lib/ 目录："
echo "1. mysql-connector-java-8.0.33.jar"
echo "2. jstl-1.2.jar"
echo "3. standard-1.1.2.jar"
echo "4. commons-dbcp2-2.9.0.jar"
echo "5. commons-pool2-2.11.1.jar"
echo "6. commons-logging-1.2.jar"
```

## Maven依赖（如果使用Maven）

如果您想转换为Maven项目，可以使用以下依赖：

```xml
<dependencies>
    <!-- MySQL驱动 -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>8.0.33</version>
    </dependency>
    
    <!-- Servlet API -->
    <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>4.0.1</version>
        <scope>provided</scope>
    </dependency>
    
    <!-- JSP API -->
    <dependency>
        <groupId>javax.servlet.jsp</groupId>
        <artifactId>javax.servlet.jsp-api</artifactId>
        <version>2.3.3</version>
        <scope>provided</scope>
    </dependency>
    
    <!-- JSTL -->
    <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>jstl</artifactId>
        <version>1.2</version>
    </dependency>
    
    <!-- 数据库连接池 -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-dbcp2</artifactId>
        <version>2.9.0</version>
    </dependency>
    
    <!-- 对象池 -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
        <version>2.11.1</version>
    </dependency>
    
    <!-- 日志 -->
    <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging</artifactId>
        <version>1.2</version>
    </dependency>
</dependencies>
```

## 直接下载链接

### 核心必需JAR包
1. **MySQL驱动**
   - 直接下载：https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar

2. **JSTL**
   - 直接下载：https://repo1.maven.org/maven2/javax/servlet/jstl/1.2/jstl-1.2.jar

3. **Standard标签库**
   - 直接下载：https://repo1.maven.org/maven2/taglibs/standard/1.1.2/standard-1.1.2.jar

### 可选JAR包
4. **Commons DBCP2**
   - 直接下载：https://repo1.maven.org/maven2/org/apache/commons/commons-dbcp2/2.9.0/commons-dbcp2-2.9.0.jar

5. **Commons Pool2**
   - 直接下载：https://repo1.maven.org/maven2/org/apache/commons/commons-pool2/2.11.1/commons-pool2-2.11.1.jar

6. **Commons Logging**
   - 直接下载：https://repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar

## 验证JAR包

下载完成后，请确保：
1. 所有JAR包都放在 `web/WEB-INF/lib/` 目录下
2. JAR包文件完整，没有损坏
3. 重启Tomcat服务器

## 常见问题

### Q: ClassNotFoundException
A: 检查JAR包是否正确放置在 `web/WEB-INF/lib/` 目录

### Q: 数据库连接失败
A: 确保mysql-connector-java版本与MySQL服务器版本兼容

### Q: JSP标签不识别
A: 确保jstl.jar和standard.jar都已添加

### Q: 连接池配置错误
A: 检查commons-dbcp2和commons-pool2是否都已添加

## 注意事项

1. **版本兼容性**：确保JAR包版本与JDK版本兼容
2. **Tomcat版本**：确保Servlet API版本与Tomcat版本匹配
3. **MySQL版本**：MySQL驱动版本应与数据库服务器版本兼容
4. **文件权限**：确保Tomcat有读取JAR包的权限

---

**下载完成后，请重启Tomcat服务器使JAR包生效！**
