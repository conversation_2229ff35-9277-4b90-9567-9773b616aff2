@echo off
echo ========================================
echo 数据库连接测试
echo ========================================
echo.

echo 正在测试MySQL连接...
mysql -u root -p123456 -e "SHOW DATABASES;" 2>nul
if %errorlevel% equ 0 (
    echo ✓ MySQL连接成功
    echo.
    echo 检查neijiang_mobile数据库...
    mysql -u root -p123456 -e "USE neijiang_mobile; SHOW TABLES;" 2>nul
    if %errorlevel% equ 0 (
        echo ✓ neijiang_mobile数据库存在
    ) else (
        echo ✗ neijiang_mobile数据库不存在，正在创建...
        mysql -u root -p123456 < sql\init.sql
        if %errorlevel% equ 0 (
            echo ✓ 数据库创建成功
        ) else (
            echo ✗ 数据库创建失败
        )
    )
) else (
    echo ✗ MySQL连接失败
    echo 请检查：
    echo 1. MySQL服务是否启动
    echo 2. 用户名密码是否正确（当前配置：root/123456）
    echo 3. 端口3306是否可用
)

echo.
echo ========================================
pause
