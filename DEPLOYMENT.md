# 内江移动支撑项目平台系统 - 部署指南

## 快速部署步骤

### 1. 环境准备

#### 1.1 安装Java环境
```bash
# 检查Java版本（需要JDK 8+）
java -version
javac -version
```

#### 1.2 安装MySQL数据库
```bash
# 下载并安装MySQL 8.0+
# 启动MySQL服务
sudo systemctl start mysql
# 设置开机自启
sudo systemctl enable mysql
```

#### 1.3 安装Tomcat服务器
```bash
# 下载Tomcat 9.0+
wget https://downloads.apache.org/tomcat/tomcat-9/v9.0.xx/bin/apache-tomcat-9.0.xx.tar.gz
# 解压到指定目录
tar -xzf apache-tomcat-9.0.xx.tar.gz -C /opt/
# 设置环境变量
export CATALINA_HOME=/opt/apache-tomcat-9.0.xx
```

### 2. 数据库配置

#### 2.1 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE neijiang_mobile CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'neijiang'@'localhost' IDENTIFIED BY 'neijiang123';
GRANT ALL PRIVILEGES ON neijiang_mobile.* TO 'neijiang'@'localhost';
FLUSH PRIVILEGES;
```

#### 2.2 导入初始数据
```bash
# 导入数据库脚本
mysql -u root -p neijiang_mobile < sql/init.sql
```

#### 2.3 验证数据库
```sql
-- 检查表是否创建成功
USE neijiang_mobile;
SHOW TABLES;

-- 检查初始数据
SELECT * FROM users;
SELECT * FROM websites;
```

### 3. 项目配置

#### 3.1 修改数据库配置
编辑 `src/db.properties` 文件：
```properties
db.driver=com.mysql.cj.jdbc.Driver
db.url=**************************************************************************************************
db.username=root
db.password=your_password
```

#### 3.2 添加MySQL驱动
下载MySQL JDBC驱动并放置到以下位置：
- `web/WEB-INF/lib/mysql-connector-java-8.0.xx.jar`
- 或者添加到Tomcat的 `lib` 目录

### 4. 编译和打包

#### 4.1 使用IntelliJ IDEA
1. 打开项目
2. 配置项目SDK（JDK 8+）
3. 配置Tomcat服务器
4. 构建项目（Build → Build Project）

#### 4.2 手动编译（可选）
```bash
# 创建编译目录
mkdir -p web/WEB-INF/classes

# 编译Java源文件
javac -cp "web/WEB-INF/lib/*:$CATALINA_HOME/lib/*" \
      -d web/WEB-INF/classes \
      src/com/neijiang/mobile/**/*.java

# 复制配置文件
cp src/db.properties web/WEB-INF/classes/
```

### 5. 部署到Tomcat

#### 5.1 方式一：直接部署
```bash
# 复制项目到Tomcat webapps目录
cp -r web/ $CATALINA_HOME/webapps/nanning/
```

#### 5.2 方式二：WAR包部署
```bash
# 创建WAR包
cd web
jar -cvf nanning.war *
# 复制到Tomcat
cp nanning.war $CATALINA_HOME/webapps/
```

### 6. 启动服务

#### 6.1 启动Tomcat
```bash
# 启动Tomcat
$CATALINA_HOME/bin/startup.sh

# 查看日志
tail -f $CATALINA_HOME/logs/catalina.out
```

#### 6.2 验证部署
访问以下URL验证部署是否成功：
- 主页：`http://localhost:8080/nanning/`
- 登录页：`http://localhost:8080/nanning/login.jsp`
- 网站导航：`http://localhost:8080/nanning/website/category`

### 7. 测试系统功能

#### 7.1 登录测试
使用默认账号登录：
- 管理员：`admin` / `admin123`
- 普通用户：`user` / `user123`

#### 7.2 功能测试
1. 用户管理功能
2. 网站管理功能
3. 权限控制功能
4. 数据统计功能

### 8. 生产环境配置

#### 8.1 安全配置
```xml
<!-- 在web.xml中添加安全配置 -->
<security-constraint>
    <web-resource-collection>
        <web-resource-name>Admin Area</web-resource-name>
        <url-pattern>/admin/*</url-pattern>
    </web-resource-collection>
    <auth-constraint>
        <role-name>admin</role-name>
    </auth-constraint>
</security-constraint>
```

#### 8.2 性能优化
```properties
# 数据库连接池配置
db.initialSize=10
db.maxActive=50
db.maxIdle=20
db.minIdle=10
db.maxWait=60000
```

#### 8.3 日志配置
```xml
<!-- 在web.xml中配置日志 -->
<context-param>
    <param-name>log4j.configuration</param-name>
    <param-value>log4j.properties</param-value>
</context-param>
```

### 9. 常见问题解决

#### 9.1 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查防火墙设置
sudo ufw status

# 检查MySQL用户权限
mysql -u root -p -e "SELECT user,host FROM mysql.user;"
```

#### 9.2 Tomcat启动失败
```bash
# 检查端口占用
netstat -tulpn | grep :8080

# 检查Java环境
echo $JAVA_HOME

# 查看Tomcat日志
cat $CATALINA_HOME/logs/catalina.out
```

#### 9.3 页面访问404
```bash
# 检查项目部署路径
ls -la $CATALINA_HOME/webapps/

# 检查web.xml配置
cat web/WEB-INF/web.xml
```

### 10. 监控和维护

#### 10.1 日志监控
```bash
# 监控Tomcat访问日志
tail -f $CATALINA_HOME/logs/localhost_access_log.*.txt

# 监控应用日志
tail -f $CATALINA_HOME/logs/catalina.out
```

#### 10.2 数据库维护
```sql
-- 定期清理系统日志
DELETE FROM system_logs WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);

-- 优化数据库表
OPTIMIZE TABLE users, websites, organizations;
```

#### 10.3 备份策略
```bash
# 数据库备份
mysqldump -u root -p neijiang_mobile > backup_$(date +%Y%m%d).sql

# 应用备份
tar -czf nanning_backup_$(date +%Y%m%d).tar.gz web/
```

## 联系支持

如果在部署过程中遇到问题，请联系：
- 技术支持：<EMAIL>
- 项目地址：[GitHub Repository]
- 文档地址：[Documentation]

---

**祝您部署顺利！**
