# 项目诊断脚本
Write-Host "========================================" -ForegroundColor Green
Write-Host "内江移动支撑项目平台系统 - 诊断脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# 检查端口
Write-Host "[1] 检查端口8080..." -ForegroundColor Yellow
$port8080 = netstat -an | Select-String ":8080"
if ($port8080) {
    Write-Host "✅ 端口8080正在使用" -ForegroundColor Green
    $port8080
} else {
    Write-Host "❌ 端口8080未被使用" -ForegroundColor Red
}
Write-Host ""

# 检查Java进程
Write-Host "[2] 检查Java进程..." -ForegroundColor Yellow
$javaProcesses = Get-Process | Where-Object {$_.ProcessName -like "*java*"}
if ($javaProcesses) {
    Write-Host "✅ 发现Java进程:" -ForegroundColor Green
    $javaProcesses | Format-Table ProcessName, Id, CPU -AutoSize
} else {
    Write-Host "❌ 未发现Java进程" -ForegroundColor Red
}
Write-Host ""

# 检查项目文件
Write-Host "[3] 检查项目文件..." -ForegroundColor Yellow
$projectFiles = @(
    "out\artifacts\nanning_war_exploded\index.jsp",
    "out\artifacts\nanning_war_exploded\WEB-INF\web.xml",
    "out\artifacts\nanning_war_exploded\WEB-INF\classes\com\neijiang\mobile\servlet\UserServlet.class"
)

foreach ($file in $projectFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file 存在" -ForegroundColor Green
    } else {
        Write-Host "❌ $file 不存在" -ForegroundColor Red
    }
}
Write-Host ""

# 测试URL连接
Write-Host "[4] 测试URL连接..." -ForegroundColor Yellow
$urls = @(
    "http://localhost:8080/",
    "http://localhost:8080/nanning/",
    "http://localhost:8080/nanning/index.jsp"
)

foreach ($url in $urls) {
    try {
        $response = Invoke-WebRequest -Uri $url -TimeoutSec 5 -ErrorAction Stop
        Write-Host "✅ $url - 状态码: $($response.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Host "❌ $url - 错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}
Write-Host ""

Write-Host "========================================" -ForegroundColor Green
Write-Host "诊断完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "💡 建议的访问URL:" -ForegroundColor Cyan
Write-Host "   http://localhost:8080/nanning/" -ForegroundColor White
Write-Host "   http://localhost:8080/nanning/index.jsp" -ForegroundColor White
Write-Host ""
Write-Host "🔑 测试账号:" -ForegroundColor Cyan
Write-Host "   管理员: admin / admin123" -ForegroundColor White
Write-Host "   普通用户: user / user123" -ForegroundColor White
