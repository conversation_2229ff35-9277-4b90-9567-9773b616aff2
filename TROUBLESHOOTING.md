# 故障排除指南

## 🚨 "找不到此 localhost 页" 错误解决方案

### 问题现象
访问 `http://localhost:8080/nanning_war_exploded/` 时显示"找不到此 localhost 页"

### 可能原因和解决方案

#### 1. Tomcat服务器未启动
**检查方法：**
```bash
# Windows
netstat -an | findstr :8080

# Linux/Mac
netstat -an | grep :8080
```

**解决方案：**
```bash
# 启动Tomcat
cd $CATALINA_HOME/bin
./startup.sh    # Linux/Mac
startup.bat     # Windows
```

#### 2. 项目未正确部署
**检查方法：**
查看Tomcat webapps目录是否有项目文件：
```bash
ls $CATALINA_HOME/webapps/
```

**解决方案：**
重新部署项目到正确位置

#### 3. 端口被占用
**检查方法：**
```bash
# Windows
netstat -ano | findstr :8080

# Linux/Mac
lsof -i :8080
```

**解决方案：**
- 关闭占用端口的程序
- 或修改Tomcat端口配置

#### 4. 项目路径配置错误
**当前错误URL：** `http://localhost:8080/nanning_war_exploded/`
**正确URL应该是：** `http://localhost:8080/nanning/`

## 🛠️ 完整部署步骤

### 步骤1：确保JAR包已下载
```bash
# 检查JAR包是否存在
ls web/WEB-INF/lib/
```
应该看到以下文件：
- mysql-connector-java-8.0.33.jar
- jstl-1.2.jar
- standard-1.1.2.jar

### 步骤2：配置数据库
```sql
-- 1. 创建数据库
CREATE DATABASE neijiang_mobile CHARACTER SET utf8mb4;

-- 2. 导入数据
mysql -u root -p neijiang_mobile < sql/init.sql
```

### 步骤3：修改数据库配置
编辑 `src/db.properties`：
```properties
db.driver=com.mysql.cj.jdbc.Driver
db.url=**************************************************************************************************
db.username=root
db.password=你的密码
```

### 步骤4：编译项目
在IntelliJ IDEA中：
1. Build → Build Project
2. 确保没有编译错误

### 步骤5：正确部署到Tomcat

#### 方法A：使用IntelliJ IDEA部署（推荐）
1. Run → Edit Configurations
2. 添加Tomcat Server → Local
3. 配置Tomcat安装路径
4. 在Deployment标签页添加Artifact
5. 设置Application context为 `/nanning`
6. 点击Run启动

#### 方法B：手动部署
```bash
# 1. 停止Tomcat
$CATALINA_HOME/bin/shutdown.sh

# 2. 清理旧部署
rm -rf $CATALINA_HOME/webapps/nanning*

# 3. 复制项目文件
cp -r web/ $CATALINA_HOME/webapps/nanning/

# 4. 复制编译后的class文件
cp -r out/production/nanning/* $CATALINA_HOME/webapps/nanning/WEB-INF/classes/

# 5. 启动Tomcat
$CATALINA_HOME/bin/startup.sh
```

### 步骤6：验证部署
1. 等待Tomcat完全启动（查看日志）
2. 访问：`http://localhost:8080/nanning/`
3. 应该看到项目首页

## 🔍 调试方法

### 1. 查看Tomcat日志
```bash
# 实时查看日志
tail -f $CATALINA_HOME/logs/catalina.out

# 查看错误日志
cat $CATALINA_HOME/logs/localhost.*.log
```

### 2. 检查项目结构
确保部署后的目录结构正确：
```
$CATALINA_HOME/webapps/nanning/
├── WEB-INF/
│   ├── web.xml
│   ├── classes/
│   │   └── com/neijiang/mobile/...
│   └── lib/
│       ├── mysql-connector-java-8.0.33.jar
│       └── ...
├── index.jsp
├── login.jsp
└── ...
```

### 3. 测试数据库连接
运行测试类：
```bash
java -cp "web/WEB-INF/lib/*:web/WEB-INF/classes" com.neijiang.mobile.test.SystemTest
```

## 🚀 快速修复命令

### 一键重新部署脚本
```bash
#!/bin/bash
echo "开始重新部署项目..."

# 停止Tomcat
$CATALINA_HOME/bin/shutdown.sh
sleep 5

# 清理旧部署
rm -rf $CATALINA_HOME/webapps/nanning*

# 重新部署
cp -r web/ $CATALINA_HOME/webapps/nanning/

# 确保JAR包存在
if [ ! -f "web/WEB-INF/lib/mysql-connector-java-8.0.33.jar" ]; then
    echo "警告：MySQL驱动JAR包不存在，请运行 download-jars.sh"
fi

# 启动Tomcat
$CATALINA_HOME/bin/startup.sh

echo "部署完成，请等待Tomcat启动后访问：http://localhost:8080/nanning/"
```

## 📞 常见错误代码

| 错误 | 原因 | 解决方案 |
|------|------|----------|
| 404 | 项目未部署或路径错误 | 检查部署路径和URL |
| 500 | 服务器内部错误 | 查看Tomcat日志，检查代码错误 |
| 连接被拒绝 | Tomcat未启动 | 启动Tomcat服务器 |
| ClassNotFoundException | JAR包缺失 | 下载并放置必需的JAR包 |
| SQLException | 数据库连接失败 | 检查数据库配置和服务状态 |

## 🎯 下一步操作建议

1. **立即执行：** 按照上述步骤重新部署项目
2. **验证：** 确保所有JAR包都在正确位置
3. **测试：** 访问 `http://localhost:8080/nanning/` 而不是 `nanning_war_exploded`
4. **监控：** 查看Tomcat日志确认没有错误

如果问题仍然存在，请提供：
- Tomcat版本
- JDK版本  
- 具体的错误日志
- 项目部署方式（IDEA还是手动）
