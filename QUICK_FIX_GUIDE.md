# 快速修复指南 - 解决404错误

## 🎯 问题诊断
您的项目遇到404错误的主要原因：
1. ✅ **已解决** - 缺少必需的JAR包依赖
2. ✅ **已解决** - 项目编译问题
3. ⚠️ **需要配置** - Tomcat服务器配置

## 🚀 立即解决方案

### 方案一：使用IntelliJ IDEA运行（推荐）

1. **打开IntelliJ IDEA**
2. **配置Tomcat服务器**：
   - File → Settings → Build, Execution, Deployment → Application Servers
   - 点击 "+" 添加 Tomcat Server
   - 选择您的Tomcat安装目录

3. **创建运行配置**：
   - Run → Edit Configurations
   - 点击 "+" → Tomcat Server → Local
   - 配置以下设置：
     - Name: `Nanning Project`
     - Application server: 选择刚才配置的Tomcat
     - 在 "Deployment" 标签页中：
       - 点击 "+" → Artifact
       - 选择 `nanning:war exploded`
       - Application context: `/nanning`

4. **运行项目**：
   - 点击绿色运行按钮
   - 浏览器会自动打开：`http://localhost:8080/nanning/`

### 方案二：手动部署到Tomcat

如果您已经安装了Tomcat：

1. **复制项目到Tomcat**：
   ```cmd
   xcopy "out\artifacts\nanning_war_exploded" "%CATALINA_HOME%\webapps\nanning\" /E /I /Y
   ```

2. **启动Tomcat**：
   ```cmd
   %CATALINA_HOME%\bin\startup.bat
   ```

3. **访问项目**：
   - 打开浏览器访问：`http://localhost:8080/nanning/`

## 🔑 测试账号

项目现在使用模拟数据（不需要MySQL数据库）：

- **管理员账号**：
  - 用户名：`admin`
  - 密码：`admin123`

- **普通用户账号**：
  - 用户名：`user`
  - 密码：`user123`

## ✅ 已完成的修复

1. **添加了必需的JAR包**：
   - javax.servlet.jar
   - javax.servlet.jsp.jar
   - 其他Java EE相关JAR包

2. **修复了编译问题**：
   - 简化了DBUtil类，移除了对Apache Commons DBCP的依赖
   - 创建了MockUserService用于模拟数据

3. **编译了所有Java文件**：
   - 所有.class文件已生成在 `web/WEB-INF/classes/`
   - 项目已部署到 `out/artifacts/nanning_war_exploded/`

## 🔧 如果仍然遇到问题

### 检查端口占用
```cmd
netstat -an | findstr :8080
```

### 检查Tomcat日志
查看Tomcat的 `logs/catalina.out` 文件

### 验证项目结构
确保以下文件存在：
- `out/artifacts/nanning_war_exploded/index.jsp`
- `out/artifacts/nanning_war_exploded/WEB-INF/web.xml`
- `out/artifacts/nanning_war_exploded/WEB-INF/classes/com/neijiang/mobile/servlet/UserServlet.class`

## 📞 下一步

1. **立即测试**：使用上述方案一或方案二运行项目
2. **登录测试**：使用提供的测试账号登录
3. **功能验证**：测试用户管理、网站管理等功能
4. **数据库配置**：如需要真实数据，后续可配置MySQL数据库

---

**现在您的项目应该可以正常运行了！** 🎉
