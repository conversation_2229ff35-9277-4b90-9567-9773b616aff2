@echo off
echo ========================================
echo 内江移动支撑项目平台系统 - 测试服务器
echo ========================================
echo.

echo 正在检查Java环境...
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java环境未配置，请先安装JDK
    pause
    exit /b 1
)
echo ✅ Java环境正常

echo.
echo 正在检查项目文件...
if not exist "out\artifacts\nanning_war_exploded\index.jsp" (
    echo ❌ 项目文件不存在，请先编译项目
    pause
    exit /b 1
)
echo ✅ 项目文件存在

echo.
echo 正在检查编译文件...
if not exist "out\artifacts\nanning_war_exploded\WEB-INF\classes\com\neijiang\mobile\servlet\UserServlet.class" (
    echo ❌ 编译文件不存在，请先编译项目
    pause
    exit /b 1
)
echo ✅ 编译文件存在

echo.
echo ========================================
echo 项目状态检查完成！
echo ========================================
echo.
echo 📋 项目信息：
echo   - 项目名称：内江移动支撑项目平台系统
echo   - 部署路径：out\artifacts\nanning_war_exploded\
echo   - 访问地址：http://localhost:8080/nanning/
echo.
echo 🔑 测试账号：
echo   - 管理员：admin / admin123
echo   - 普通用户：user / user123
echo.
echo 📝 下一步操作：
echo   1. 在IntelliJ IDEA中配置Tomcat服务器
echo   2. 创建运行配置，部署路径设为：/nanning
echo   3. 运行项目并访问：http://localhost:8080/nanning/
echo.
echo 💡 提示：项目现在使用模拟数据，无需配置MySQL数据库
echo.

pause
