@echo off
echo ========================================
echo 系统状态检查工具
echo ========================================
echo.

echo [1] 检查Java环境...
java -version 2>nul
if %errorlevel% equ 0 (
    echo ✓ Java环境正常
) else (
    echo ✗ Java环境未配置或版本不兼容
)
echo.

echo [2] 检查Tomcat环境...
if "%CATALINA_HOME%"=="" (
    echo ✗ CATALINA_HOME环境变量未设置
) else (
    echo ✓ CATALINA_HOME: %CATALINA_HOME%
    if exist "%CATALINA_HOME%\bin\catalina.bat" (
        echo ✓ Tomcat安装正常
    ) else (
        echo ✗ Tomcat安装路径错误
    )
)
echo.

echo [3] 检查端口占用...
netstat -an | findstr :8080 >nul
if %errorlevel% equ 0 (
    echo ✓ 端口8080已被占用（Tomcat可能正在运行）
) else (
    echo ✗ 端口8080未被占用（Tomcat可能未启动）
)
echo.

echo [4] 检查项目文件...
if exist "web\index.jsp" (
    echo ✓ 项目web文件存在
) else (
    echo ✗ 项目web文件缺失
)

if exist "src\com\neijiang\mobile" (
    echo ✓ 项目源代码存在
) else (
    echo ✗ 项目源代码缺失
)
echo.

echo [5] 检查JAR包...
if exist "web\WEB-INF\lib\mysql-connector-java-8.0.33.jar" (
    echo ✓ MySQL驱动存在
) else (
    echo ✗ MySQL驱动缺失
)

if exist "web\WEB-INF\lib\jstl-1.2.jar" (
    echo ✓ JSTL库存在
) else (
    echo ✗ JSTL库缺失
)
echo.

echo [6] 检查数据库配置...
if exist "src\db.properties" (
    echo ✓ 数据库配置文件存在
    echo 配置内容：
    type "src\db.properties"
) else (
    echo ✗ 数据库配置文件缺失
)
echo.

echo [7] 检查项目部署...
if "%CATALINA_HOME%" neq "" (
    if exist "%CATALINA_HOME%\webapps\nanning" (
        echo ✓ 项目已部署到Tomcat
        echo 部署路径: %CATALINA_HOME%\webapps\nanning
    ) else (
        echo ✗ 项目未部署到Tomcat
    )
) else (
    echo ✗ 无法检查部署状态（CATALINA_HOME未设置）
)
echo.

echo [8] 测试网络连接...
ping -n 1 localhost >nul
if %errorlevel% equ 0 (
    echo ✓ 本地网络连接正常
) else (
    echo ✗ 本地网络连接异常
)
echo.

echo ========================================
echo 检查完成！
echo ========================================
echo.

echo 建议的解决步骤：
echo 1. 如果JAR包缺失，请运行：download-jars.bat
echo 2. 如果项目未部署，请运行：deploy.bat
echo 3. 如果数据库配置有问题，请编辑：src\db.properties
echo 4. 如果Tomcat未启动，请运行：%CATALINA_HOME%\bin\startup.bat
echo.

echo 正确的访问地址应该是：
echo http://localhost:8080/nanning/
echo.

pause
