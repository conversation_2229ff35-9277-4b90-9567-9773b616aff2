package com.neijiang.mobile.filter;

import com.neijiang.mobile.model.User;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 登录验证过滤器
 */
public class LoginFilter implements Filter {
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // 初始化
    }
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) 
            throws IOException, ServletException {
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 获取请求URI
        String uri = httpRequest.getRequestURI();
        String contextPath = httpRequest.getContextPath();
        
        // 排除不需要验证的路径
        if (isExcludedPath(uri, contextPath)) {
            chain.doFilter(request, response);
            return;
        }
        
        // 检查用户是否已登录
        HttpSession session = httpRequest.getSession(false);
        User user = null;
        
        if (session != null) {
            user = (User) session.getAttribute("user");
        }
        
        if (user == null) {
            // 用户未登录，重定向到登录页面
            String loginUrl = contextPath + "/login.jsp";
            
            // 如果是AJAX请求，返回JSON响应
            String requestedWith = httpRequest.getHeader("X-Requested-With");
            if ("XMLHttpRequest".equals(requestedWith)) {
                httpResponse.setContentType("application/json;charset=UTF-8");
                httpResponse.getWriter().write("{\"error\":\"未登录\",\"redirect\":\"" + loginUrl + "\"}");
                return;
            }
            
            // 保存原始请求URL，登录后可以重定向回来
            String originalUrl = httpRequest.getRequestURL().toString();
            String queryString = httpRequest.getQueryString();
            if (queryString != null) {
                originalUrl += "?" + queryString;
            }
            session = httpRequest.getSession(true);
            session.setAttribute("originalUrl", originalUrl);
            
            httpResponse.sendRedirect(loginUrl);
            return;
        }
        
        // 检查管理员权限
        if (isAdminPath(uri, contextPath) && user.getRoleId() != 1) {
            httpResponse.sendError(HttpServletResponse.SC_FORBIDDEN, "权限不足");
            return;
        }
        
        // 用户已登录且有权限，继续处理请求
        chain.doFilter(request, response);
    }
    
    @Override
    public void destroy() {
        // 清理资源
    }
    
    /**
     * 检查是否为排除路径（不需要登录验证）
     */
    private boolean isExcludedPath(String uri, String contextPath) {
        String path = uri.substring(contextPath.length());
        
        // 排除的路径列表
        String[] excludedPaths = {
            "/login.jsp",
            "/register.jsp",
            "/index.jsp",
            "/user/login",
            "/user/register",
            "/website/category",
            "/website/view",
            "/website/visit",
            "/css/",
            "/js/",
            "/images/",
            "/error/"
        };
        
        for (String excludedPath : excludedPaths) {
            if (path.startsWith(excludedPath)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否为管理员路径
     */
    private boolean isAdminPath(String uri, String contextPath) {
        String path = uri.substring(contextPath.length());
        
        // 需要管理员权限的路径
        String[] adminPaths = {
            "/admin/",
            "/user/list",
            "/user/edit",
            "/user/delete",
            "/website/add",
            "/website/edit",
            "/website/delete",
            "/website/stats"
        };
        
        for (String adminPath : adminPaths) {
            if (path.startsWith(adminPath)) {
                return true;
            }
        }
        
        return false;
    }
}
