package com.neijiang.mobile.test;

import com.neijiang.mobile.model.User;
import com.neijiang.mobile.model.Website;
import com.neijiang.mobile.service.UserService;
import com.neijiang.mobile.service.WebsiteService;
import com.neijiang.mobile.util.DBUtil;

/**
 * 系统功能测试类
 */
public class SystemTest {
    
    public static void main(String[] args) {
        System.out.println("=== 内江移动支撑项目平台系统测试 ===");
        
        // 测试数据库连接
        testDatabaseConnection();
        
        // 测试用户服务
        testUserService();
        
        // 测试网站服务
        testWebsiteService();
        
        System.out.println("=== 测试完成 ===");
    }
    
    /**
     * 测试数据库连接
     */
    private static void testDatabaseConnection() {
        System.out.println("\n--- 测试数据库连接 ---");
        try {
            boolean connected = DBUtil.testConnection();
            if (connected) {
                System.out.println("✓ 数据库连接成功");
            } else {
                System.out.println("✗ 数据库连接失败");
            }
        } catch (Exception e) {
            System.out.println("✗ 数据库连接异常: " + e.getMessage());
        }
    }
    
    /**
     * 测试用户服务
     */
    private static void testUserService() {
        System.out.println("\n--- 测试用户服务 ---");
        UserService userService = new UserService();
        
        try {
            // 测试用户登录
            User loginUser = userService.login("admin", "admin123");
            if (loginUser != null) {
                System.out.println("✓ 管理员登录成功: " + loginUser.getRealName());
            } else {
                System.out.println("✗ 管理员登录失败");
            }
            
            // 测试获取用户列表
            var users = userService.getAllUsers();
            System.out.println("✓ 获取用户列表成功，共 " + users.size() + " 个用户");
            
            // 显示用户信息
            for (User user : users) {
                System.out.println("  - " + user.getUsername() + " (" + user.getRealName() + ")");
            }
            
        } catch (Exception e) {
            System.out.println("✗ 用户服务测试异常: " + e.getMessage());
        }
    }
    
    /**
     * 测试网站服务
     */
    private static void testWebsiteService() {
        System.out.println("\n--- 测试网站服务 ---");
        WebsiteService websiteService = new WebsiteService();
        
        try {
            // 测试获取网站列表
            var websites = websiteService.getAllWebsites();
            System.out.println("✓ 获取网站列表成功，共 " + websites.size() + " 个网站");
            
            // 显示网站信息
            for (Website website : websites) {
                System.out.println("  - " + website.getSiteName() + " (" + website.getCategory() + ")");
                System.out.println("    URL: " + website.getSiteUrl());
                System.out.println("    访问次数: " + website.getVisitCount());
            }
            
            // 测试网站统计
            var stats = websiteService.getWebsiteStats();
            System.out.println("✓ 网站统计信息:");
            System.out.println("  - 总数: " + stats.getTotalCount());
            System.out.println("  - 正常: " + stats.getActiveCount());
            System.out.println("  - 维护: " + stats.getMaintenanceCount());
            System.out.println("  - 停用: " + stats.getDisabledCount());
            System.out.println("  - 总访问: " + stats.getTotalVisits());
            
        } catch (Exception e) {
            System.out.println("✗ 网站服务测试异常: " + e.getMessage());
        }
    }
}
