package com.neijiang.mobile.util;

import java.sql.*;
import java.util.Properties;
import java.io.InputStream;

/**
 * 数据库连接工具类（简化版本）
 */
public class DBUtil {
    private static String driver;
    private static String url;
    private static String username;
    private static String password;

    static {
        try {
            // 加载数据库配置
            Properties props = new Properties();
            InputStream is = DBUtil.class.getClassLoader().getResourceAsStream("db.properties");
            if (is != null) {
                props.load(is);
                driver = props.getProperty("db.driver", "com.mysql.cj.jdbc.Driver");
                url = props.getProperty("db.url", "**************************************************************************************************");
                username = props.getProperty("db.username", "root");
                password = props.getProperty("db.password", "123456");
            } else {
                // 默认配置
                driver = "com.mysql.cj.jdbc.Driver";
                url = "**************************************************************************************************";
                username = "root";
                password = "123456";
            }

            // 加载驱动
            try {
                Class.forName(driver);
            } catch (ClassNotFoundException e) {
                System.out.println("警告：MySQL驱动未找到，将使用模拟数据");
            }

        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("警告：数据库配置初始化失败，将使用模拟数据");
        }
    }


    
    /**
     * 获取数据库连接
     */
    public static Connection getConnection() throws SQLException {
        try {
            return DriverManager.getConnection(url, username, password);
        } catch (SQLException e) {
            System.out.println("警告：无法连接到MySQL数据库，请检查数据库配置");
            throw new SQLException("数据库连接失败：" + e.getMessage());
        }
    }
    
    /**
     * 关闭数据库连接
     */
    public static void closeConnection(Connection conn) {
        if (conn != null) {
            try {
                conn.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 关闭PreparedStatement
     */
    public static void closePreparedStatement(PreparedStatement pstmt) {
        if (pstmt != null) {
            try {
                pstmt.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 关闭ResultSet
     */
    public static void closeResultSet(ResultSet rs) {
        if (rs != null) {
            try {
                rs.close();
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 关闭所有资源
     */
    public static void closeAll(Connection conn, PreparedStatement pstmt, ResultSet rs) {
        closeResultSet(rs);
        closePreparedStatement(pstmt);
        closeConnection(conn);
    }
    
    /**
     * 测试数据库连接
     */
    public static boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            System.err.println("数据库连接测试失败: " + e.getMessage());
            return false;
        }
    }
}
