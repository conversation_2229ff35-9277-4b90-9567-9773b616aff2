package com.neijiang.mobile.model;

import java.util.Date;

/**
 * 组织机构实体类
 */
public class Organization {
    private int orgId;
    private String orgName;
    private String orgCode;
    private int parentId; // 父级组织ID
    private String orgType; // 组织类型：部门、分公司等
    private String description;
    private String manager; // 负责人
    private String contact; // 联系方式
    private String address; // 地址
    private Date createTime;
    private Date updateTime;
    private int status; // 0-停用 1-正常
    private int level; // 组织层级
    private String path; // 组织路径

    public Organization() {}

    public Organization(String orgName, String orgCode, int parentId, String orgType) {
        this.orgName = orgName;
        this.orgCode = orgCode;
        this.parentId = parentId;
        this.orgType = orgType;
        this.status = 1;
        this.createTime = new Date();
        this.updateTime = new Date();
    }

    // Getters and Setters
    public int getOrgId() {
        return orgId;
    }

    public void setOrgId(int orgId) {
        this.orgId = orgId;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public int getParentId() {
        return parentId;
    }

    public void setParentId(int parentId) {
        this.parentId = parentId;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    @Override
    public String toString() {
        return "Organization{" +
                "orgId=" + orgId +
                ", orgName='" + orgName + '\'' +
                ", orgCode='" + orgCode + '\'' +
                ", orgType='" + orgType + '\'' +
                ", manager='" + manager + '\'' +
                ", status=" + status +
                '}';
    }
}
