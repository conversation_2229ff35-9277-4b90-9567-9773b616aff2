package com.neijiang.mobile.model;

import java.util.Date;

/**
 * 12580网站实体类
 */
public class Website {
    private int websiteId;
    private String siteName;
    private String siteUrl;
    private String description;
    private String category;
    private int managerId; // 管理员ID
    private Date createTime;
    private Date updateTime;
    private int status; // 0-停用 1-正常 2-维护中
    private int visitCount; // 访问次数
    private String keywords; // 关键词
    private String contact; // 联系方式

    public Website() {}

    public Website(String siteName, String siteUrl, String description, String category) {
        this.siteName = siteName;
        this.siteUrl = siteUrl;
        this.description = description;
        this.category = category;
        this.status = 1;
        this.visitCount = 0;
        this.createTime = new Date();
        this.updateTime = new Date();
    }

    // Getters and Setters
    public int getWebsiteId() {
        return websiteId;
    }

    public void setWebsiteId(int websiteId) {
        this.websiteId = websiteId;
    }

    public String getSiteName() {
        return siteName;
    }

    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }

    public String getSiteUrl() {
        return siteUrl;
    }

    public void setSiteUrl(String siteUrl) {
        this.siteUrl = siteUrl;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public int getManagerId() {
        return managerId;
    }

    public void setManagerId(int managerId) {
        this.managerId = managerId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getVisitCount() {
        return visitCount;
    }

    public void setVisitCount(int visitCount) {
        this.visitCount = visitCount;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    @Override
    public String toString() {
        return "Website{" +
                "websiteId=" + websiteId +
                ", siteName='" + siteName + '\'' +
                ", siteUrl='" + siteUrl + '\'' +
                ", category='" + category + '\'' +
                ", status=" + status +
                ", visitCount=" + visitCount +
                '}';
    }
}
