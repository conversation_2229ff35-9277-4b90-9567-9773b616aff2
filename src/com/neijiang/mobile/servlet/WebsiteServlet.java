package com.neijiang.mobile.servlet;

import com.neijiang.mobile.model.Website;
import com.neijiang.mobile.service.WebsiteService;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 网站管理控制器
 */
@WebServlet("/website/*")
public class WebsiteServlet extends HttpServlet {
    private WebsiteService websiteService;
    
    @Override
    public void init() throws ServletException {
        websiteService = new WebsiteService();
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = getAction(request);
        
        switch (action) {
            case "list":
                listWebsites(request, response);
                break;
            case "view":
                viewWebsite(request, response);
                break;
            case "edit":
                editWebsite(request, response);
                break;
            case "delete":
                deleteWebsite(request, response);
                break;
            case "visit":
                visitWebsite(request, response);
                break;
            case "category":
                listWebsitesByCategory(request, response);
                break;
            case "stats":
                getWebsiteStats(request, response);
                break;
            default:
                listWebsites(request, response);
                break;
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = getAction(request);
        
        switch (action) {
            case "add":
                addWebsite(request, response);
                break;
            case "update":
                updateWebsite(request, response);
                break;
            case "toggleStatus":
                toggleWebsiteStatus(request, response);
                break;
            case "toggleMaintenance":
                toggleMaintenanceMode(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_BAD_REQUEST);
                break;
        }
    }
    
    /**
     * 添加网站
     */
    private void addWebsite(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        Website website = new Website();
        website.setSiteName(request.getParameter("siteName"));
        website.setSiteUrl(request.getParameter("siteUrl"));
        website.setDescription(request.getParameter("description"));
        website.setCategory(request.getParameter("category"));
        website.setKeywords(request.getParameter("keywords"));
        website.setContact(request.getParameter("contact"));
        
        String managerIdStr = request.getParameter("managerId");
        if (managerIdStr != null && !managerIdStr.trim().isEmpty()) {
            website.setManagerId(Integer.parseInt(managerIdStr));
        }
        
        boolean success = websiteService.addWebsite(website);
        
        if (success) {
            response.sendRedirect(request.getContextPath() + "/website/list");
        } else {
            request.setAttribute("error", "添加网站失败");
            request.setAttribute("website", website);
            request.getRequestDispatcher("/admin/website_form.jsp").forward(request, response);
        }
    }
    
    /**
     * 更新网站
     */
    private void updateWebsite(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        Website website = new Website();
        website.setWebsiteId(Integer.parseInt(request.getParameter("websiteId")));
        website.setSiteName(request.getParameter("siteName"));
        website.setSiteUrl(request.getParameter("siteUrl"));
        website.setDescription(request.getParameter("description"));
        website.setCategory(request.getParameter("category"));
        website.setKeywords(request.getParameter("keywords"));
        website.setContact(request.getParameter("contact"));
        website.setStatus(Integer.parseInt(request.getParameter("status")));
        
        String managerIdStr = request.getParameter("managerId");
        if (managerIdStr != null && !managerIdStr.trim().isEmpty()) {
            website.setManagerId(Integer.parseInt(managerIdStr));
        }
        
        boolean success = websiteService.updateWebsite(website);
        
        if (success) {
            response.sendRedirect(request.getContextPath() + "/website/list");
        } else {
            request.setAttribute("error", "更新网站失败");
            request.setAttribute("website", website);
            request.getRequestDispatcher("/admin/website_form.jsp").forward(request, response);
        }
    }
    
    /**
     * 查看网站列表
     */
    private void listWebsites(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<Website> websites = websiteService.getAllWebsites();
        request.setAttribute("websites", websites);
        request.getRequestDispatcher("/admin/website_list.jsp").forward(request, response);
    }
    
    /**
     * 根据分类查看网站列表
     */
    private void listWebsitesByCategory(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String category = request.getParameter("cat");
        List<Website> websites;
        
        if (category != null && !category.trim().isEmpty()) {
            websites = websiteService.getWebsitesByCategory(category);
        } else {
            websites = websiteService.getAllWebsites();
        }
        
        request.setAttribute("websites", websites);
        request.setAttribute("currentCategory", category);
        request.getRequestDispatcher("/website_list.jsp").forward(request, response);
    }
    
    /**
     * 查看网站详情
     */
    private void viewWebsite(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int websiteId = Integer.parseInt(request.getParameter("id"));
        Website website = websiteService.getWebsiteById(websiteId);
        
        if (website != null) {
            request.setAttribute("website", website);
            request.getRequestDispatcher("/website_detail.jsp").forward(request, response);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    /**
     * 编辑网站
     */
    private void editWebsite(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int websiteId = Integer.parseInt(request.getParameter("id"));
        Website website = websiteService.getWebsiteById(websiteId);
        
        if (website != null) {
            request.setAttribute("website", website);
            request.setAttribute("isEdit", true);
            request.getRequestDispatcher("/admin/website_form.jsp").forward(request, response);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    /**
     * 删除网站
     */
    private void deleteWebsite(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int websiteId = Integer.parseInt(request.getParameter("id"));
        boolean success = websiteService.deleteWebsite(websiteId);
        
        if (success) {
            response.sendRedirect(request.getContextPath() + "/website/list");
        } else {
            request.setAttribute("error", "删除网站失败");
            listWebsites(request, response);
        }
    }
    
    /**
     * 访问网站
     */
    private void visitWebsite(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int websiteId = Integer.parseInt(request.getParameter("id"));
        Website website = websiteService.getWebsiteById(websiteId);
        
        if (website != null) {
            // 增加访问次数
            websiteService.visitWebsite(websiteId);
            // 重定向到目标网站
            response.sendRedirect(website.getSiteUrl());
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    /**
     * 切换网站状态
     */
    private void toggleWebsiteStatus(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int websiteId = Integer.parseInt(request.getParameter("id"));
        boolean success = websiteService.toggleWebsiteStatus(websiteId);
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        if (success) {
            response.getWriter().write("{\"success\": true}");
        } else {
            response.getWriter().write("{\"success\": false}");
        }
    }
    
    /**
     * 切换维护模式
     */
    private void toggleMaintenanceMode(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int websiteId = Integer.parseInt(request.getParameter("id"));
        boolean success = websiteService.toggleMaintenanceMode(websiteId);
        
        response.setContentType("application/json");
        response.setCharacterEncoding("UTF-8");
        
        if (success) {
            response.getWriter().write("{\"success\": true}");
        } else {
            response.getWriter().write("{\"success\": false}");
        }
    }
    
    /**
     * 获取网站统计信息
     */
    private void getWebsiteStats(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        WebsiteService.WebsiteStats stats = websiteService.getWebsiteStats();
        request.setAttribute("stats", stats);
        request.getRequestDispatcher("/admin/website_stats.jsp").forward(request, response);
    }
    
    /**
     * 获取操作类型
     */
    private String getAction(HttpServletRequest request) {
        String pathInfo = request.getPathInfo();
        if (pathInfo == null || pathInfo.equals("/")) {
            return "list";
        }
        return pathInfo.substring(1);
    }
}
