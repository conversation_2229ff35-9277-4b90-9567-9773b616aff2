package com.neijiang.mobile.servlet;

import com.neijiang.mobile.model.User;
import com.neijiang.mobile.service.UserService;
import com.neijiang.mobile.service.MockUserService;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * 用户控制器
 */
@WebServlet("/user/*")
public class UserServlet extends HttpServlet {
    private UserService userService;
    private MockUserService mockUserService;
    private boolean useMockService = false;

    @Override
    public void init() throws ServletException {
        try {
            userService = new UserService();
        } catch (Exception e) {
            System.out.println("警告：数据库服务不可用，使用模拟数据服务");
            mockUserService = new MockUserService();
            useMockService = true;
        }
    }
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = getAction(request);
        
        switch (action) {
            case "list":
                listUsers(request, response);
                break;
            case "view":
                viewUser(request, response);
                break;
            case "edit":
                editUser(request, response);
                break;
            case "delete":
                deleteUser(request, response);
                break;
            case "logout":
                logout(request, response);
                break;
            default:
                listUsers(request, response);
                break;
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String action = getAction(request);
        
        switch (action) {
            case "login":
                login(request, response);
                break;
            case "register":
                register(request, response);
                break;
            case "add":
                addUser(request, response);
                break;
            case "update":
                updateUser(request, response);
                break;
            case "changePassword":
                changePassword(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_BAD_REQUEST);
                break;
        }
    }
    
    /**
     * 用户登录
     */
    private void login(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        String username = request.getParameter("username");
        String password = request.getParameter("password");

        User user = null;
        if (useMockService) {
            user = mockUserService.login(username, password);
        } else {
            user = userService.login(username, password);
        }

        if (user != null) {
            HttpSession session = request.getSession();
            session.setAttribute("user", user);
            response.sendRedirect(request.getContextPath() + "/index.jsp");
        } else {
            request.setAttribute("error", "用户名或密码错误");
            request.getRequestDispatcher("/login.jsp").forward(request, response);
        }
    }
    
    /**
     * 用户注册
     */
    private void register(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        String username = request.getParameter("username");
        String password = request.getParameter("password");
        String realName = request.getParameter("realName");
        String email = request.getParameter("email");
        String phone = request.getParameter("phone");
        
        boolean success = userService.register(username, password, realName, email, phone);
        
        if (success) {
            request.setAttribute("message", "注册成功，请登录");
            request.getRequestDispatcher("/login.jsp").forward(request, response);
        } else {
            request.setAttribute("error", "注册失败，用户名可能已存在");
            request.getRequestDispatcher("/register.jsp").forward(request, response);
        }
    }
    
    /**
     * 添加用户
     */
    private void addUser(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        User user = new User();
        user.setUsername(request.getParameter("username"));
        user.setPassword(request.getParameter("password"));
        user.setRealName(request.getParameter("realName"));
        user.setEmail(request.getParameter("email"));
        user.setPhone(request.getParameter("phone"));
        user.setDepartment(request.getParameter("department"));
        user.setPosition(request.getParameter("position"));
        user.setRoleId(Integer.parseInt(request.getParameter("roleId")));
        
        boolean success = userService.addUser(user);
        
        if (success) {
            response.sendRedirect(request.getContextPath() + "/user/list");
        } else {
            request.setAttribute("error", "添加用户失败");
            request.setAttribute("user", user);
            request.getRequestDispatcher("/admin/user_form.jsp").forward(request, response);
        }
    }
    
    /**
     * 更新用户
     */
    private void updateUser(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        User user = new User();
        user.setUserId(Integer.parseInt(request.getParameter("userId")));
        user.setUsername(request.getParameter("username"));
        user.setRealName(request.getParameter("realName"));
        user.setEmail(request.getParameter("email"));
        user.setPhone(request.getParameter("phone"));
        user.setDepartment(request.getParameter("department"));
        user.setPosition(request.getParameter("position"));
        user.setRoleId(Integer.parseInt(request.getParameter("roleId")));
        user.setStatus(Integer.parseInt(request.getParameter("status")));
        
        // 如果提供了新密码，则更新密码
        String newPassword = request.getParameter("password");
        if (newPassword != null && !newPassword.trim().isEmpty()) {
            user.setPassword(newPassword);
        }
        
        boolean success = userService.updateUser(user);
        
        if (success) {
            response.sendRedirect(request.getContextPath() + "/user/list");
        } else {
            request.setAttribute("error", "更新用户失败");
            request.setAttribute("user", user);
            request.getRequestDispatcher("/admin/user_form.jsp").forward(request, response);
        }
    }
    
    /**
     * 查看用户列表
     */
    private void listUsers(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<User> users = userService.getAllUsers();
        request.setAttribute("users", users);
        request.getRequestDispatcher("/admin/user_list.jsp").forward(request, response);
    }
    
    /**
     * 查看用户详情
     */
    private void viewUser(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int userId = Integer.parseInt(request.getParameter("id"));
        User user = userService.getUserById(userId);
        
        if (user != null) {
            request.setAttribute("user", user);
            request.getRequestDispatcher("/admin/user_detail.jsp").forward(request, response);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    /**
     * 编辑用户
     */
    private void editUser(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int userId = Integer.parseInt(request.getParameter("id"));
        User user = userService.getUserById(userId);
        
        if (user != null) {
            request.setAttribute("user", user);
            request.setAttribute("isEdit", true);
            request.getRequestDispatcher("/admin/user_form.jsp").forward(request, response);
        } else {
            response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }
    
    /**
     * 删除用户
     */
    private void deleteUser(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        int userId = Integer.parseInt(request.getParameter("id"));
        boolean success = userService.deleteUser(userId);
        
        if (success) {
            response.sendRedirect(request.getContextPath() + "/user/list");
        } else {
            request.setAttribute("error", "删除用户失败");
            listUsers(request, response);
        }
    }
    
    /**
     * 修改密码
     */
    private void changePassword(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        HttpSession session = request.getSession();
        User currentUser = (User) session.getAttribute("user");
        
        if (currentUser == null) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return;
        }
        
        String oldPassword = request.getParameter("oldPassword");
        String newPassword = request.getParameter("newPassword");
        
        boolean success = userService.changePassword(currentUser.getUserId(), oldPassword, newPassword);
        
        if (success) {
            request.setAttribute("message", "密码修改成功");
        } else {
            request.setAttribute("error", "密码修改失败，请检查原密码");
        }
        
        request.getRequestDispatcher("/user/change_password.jsp").forward(request, response);
    }
    
    /**
     * 用户登出
     */
    private void logout(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        HttpSession session = request.getSession();
        session.invalidate();
        response.sendRedirect(request.getContextPath() + "/login.jsp");
    }
    
    /**
     * 获取操作类型
     */
    private String getAction(HttpServletRequest request) {
        String pathInfo = request.getPathInfo();
        if (pathInfo == null || pathInfo.equals("/")) {
            return "list";
        }
        return pathInfo.substring(1);
    }
}
