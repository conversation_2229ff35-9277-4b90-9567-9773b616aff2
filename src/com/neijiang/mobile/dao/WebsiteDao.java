package com.neijiang.mobile.dao;

import com.neijiang.mobile.model.Website;
import com.neijiang.mobile.util.DBUtil;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 网站数据访问对象
 */
public class WebsiteDao {
    
    /**
     * 添加网站
     */
    public boolean addWebsite(Website website) {
        String sql = "INSERT INTO websites (site_name, site_url, description, category, manager_id, create_time, update_time, status, visit_count, keywords, contact) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, website.getSiteName());
            pstmt.setString(2, website.getSiteUrl());
            pstmt.setString(3, website.getDescription());
            pstmt.setString(4, website.getCategory());
            pstmt.setInt(5, website.getManagerId());
            pstmt.setTimestamp(6, new Timestamp(website.getCreateTime().getTime()));
            pstmt.setTimestamp(7, new Timestamp(website.getUpdateTime().getTime()));
            pstmt.setInt(8, website.getStatus());
            pstmt.setInt(9, website.getVisitCount());
            pstmt.setString(10, website.getKeywords());
            pstmt.setString(11, website.getContact());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            DBUtil.closeAll(conn, pstmt, null);
        }
    }
    
    /**
     * 删除网站
     */
    public boolean deleteWebsite(int websiteId) {
        String sql = "DELETE FROM websites WHERE website_id = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, websiteId);
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            DBUtil.closeAll(conn, pstmt, null);
        }
    }
    
    /**
     * 更新网站信息
     */
    public boolean updateWebsite(Website website) {
        String sql = "UPDATE websites SET site_name=?, site_url=?, description=?, category=?, manager_id=?, update_time=?, status=?, keywords=?, contact=? WHERE website_id=?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, website.getSiteName());
            pstmt.setString(2, website.getSiteUrl());
            pstmt.setString(3, website.getDescription());
            pstmt.setString(4, website.getCategory());
            pstmt.setInt(5, website.getManagerId());
            pstmt.setTimestamp(6, new Timestamp(website.getUpdateTime().getTime()));
            pstmt.setInt(7, website.getStatus());
            pstmt.setString(8, website.getKeywords());
            pstmt.setString(9, website.getContact());
            pstmt.setInt(10, website.getWebsiteId());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            DBUtil.closeAll(conn, pstmt, null);
        }
    }
    
    /**
     * 根据ID查询网站
     */
    public Website getWebsiteById(int websiteId) {
        String sql = "SELECT * FROM websites WHERE website_id = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, websiteId);
            rs = pstmt.executeQuery();
            
            if (rs.next()) {
                return extractWebsiteFromResultSet(rs);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeAll(conn, pstmt, rs);
        }
        
        return null;
    }
    
    /**
     * 查询所有网站
     */
    public List<Website> getAllWebsites() {
        String sql = "SELECT * FROM websites ORDER BY create_time DESC";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<Website> websites = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                websites.add(extractWebsiteFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeAll(conn, pstmt, rs);
        }
        
        return websites;
    }
    
    /**
     * 根据分类查询网站
     */
    public List<Website> getWebsitesByCategory(String category) {
        String sql = "SELECT * FROM websites WHERE category = ? ORDER BY create_time DESC";
        Connection conn = null;
        PreparedStatement pstmt = null;
        ResultSet rs = null;
        List<Website> websites = new ArrayList<>();
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setString(1, category);
            rs = pstmt.executeQuery();
            
            while (rs.next()) {
                websites.add(extractWebsiteFromResultSet(rs));
            }
        } catch (SQLException e) {
            e.printStackTrace();
        } finally {
            DBUtil.closeAll(conn, pstmt, rs);
        }
        
        return websites;
    }
    
    /**
     * 更新访问次数
     */
    public boolean updateVisitCount(int websiteId) {
        String sql = "UPDATE websites SET visit_count = visit_count + 1 WHERE website_id = ?";
        Connection conn = null;
        PreparedStatement pstmt = null;
        
        try {
            conn = DBUtil.getConnection();
            pstmt = conn.prepareStatement(sql);
            pstmt.setInt(1, websiteId);
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        } finally {
            DBUtil.closeAll(conn, pstmt, null);
        }
    }
    
    /**
     * 从ResultSet中提取Website对象
     */
    private Website extractWebsiteFromResultSet(ResultSet rs) throws SQLException {
        Website website = new Website();
        website.setWebsiteId(rs.getInt("website_id"));
        website.setSiteName(rs.getString("site_name"));
        website.setSiteUrl(rs.getString("site_url"));
        website.setDescription(rs.getString("description"));
        website.setCategory(rs.getString("category"));
        website.setManagerId(rs.getInt("manager_id"));
        website.setCreateTime(rs.getTimestamp("create_time"));
        website.setUpdateTime(rs.getTimestamp("update_time"));
        website.setStatus(rs.getInt("status"));
        website.setVisitCount(rs.getInt("visit_count"));
        website.setKeywords(rs.getString("keywords"));
        website.setContact(rs.getString("contact"));
        return website;
    }
}
