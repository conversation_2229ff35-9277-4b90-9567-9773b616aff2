package com.neijiang.mobile.service;

import com.neijiang.mobile.dao.WebsiteDao;
import com.neijiang.mobile.model.Website;

import java.util.Date;
import java.util.List;

/**
 * 网站业务逻辑层
 */
public class WebsiteService {
    private WebsiteDao websiteDao;
    
    public WebsiteService() {
        this.websiteDao = new WebsiteDao();
    }
    
    /**
     * 添加网站
     */
    public boolean addWebsite(Website website) {
        website.setCreateTime(new Date());
        website.setUpdateTime(new Date());
        website.setVisitCount(0);
        website.setStatus(1); // 默认正常状态
        
        return websiteDao.addWebsite(website);
    }
    
    /**
     * 删除网站
     */
    public boolean deleteWebsite(int websiteId) {
        return websiteDao.deleteWebsite(websiteId);
    }
    
    /**
     * 更新网站信息
     */
    public boolean updateWebsite(Website website) {
        website.setUpdateTime(new Date());
        return websiteDao.updateWebsite(website);
    }
    
    /**
     * 根据ID查询网站
     */
    public Website getWebsiteById(int websiteId) {
        return websiteDao.getWebsiteById(websiteId);
    }
    
    /**
     * 查询所有网站
     */
    public List<Website> getAllWebsites() {
        return websiteDao.getAllWebsites();
    }
    
    /**
     * 根据分类查询网站
     */
    public List<Website> getWebsitesByCategory(String category) {
        return websiteDao.getWebsitesByCategory(category);
    }
    
    /**
     * 访问网站（增加访问次数）
     */
    public boolean visitWebsite(int websiteId) {
        return websiteDao.updateVisitCount(websiteId);
    }
    
    /**
     * 网站维护模式切换
     */
    public boolean toggleMaintenanceMode(int websiteId) {
        Website website = websiteDao.getWebsiteById(websiteId);
        if (website == null) {
            return false;
        }
        
        // 切换维护状态：1-正常 2-维护中
        website.setStatus(website.getStatus() == 1 ? 2 : 1);
        website.setUpdateTime(new Date());
        
        return websiteDao.updateWebsite(website);
    }
    
    /**
     * 启用/停用网站
     */
    public boolean toggleWebsiteStatus(int websiteId) {
        Website website = websiteDao.getWebsiteById(websiteId);
        if (website == null) {
            return false;
        }
        
        // 切换状态：0-停用 1-正常
        website.setStatus(website.getStatus() == 0 ? 1 : 0);
        website.setUpdateTime(new Date());
        
        return websiteDao.updateWebsite(website);
    }
    
    /**
     * 获取网站统计信息
     */
    public WebsiteStats getWebsiteStats() {
        List<Website> allWebsites = websiteDao.getAllWebsites();
        
        int totalCount = allWebsites.size();
        int activeCount = 0;
        int maintenanceCount = 0;
        int disabledCount = 0;
        int totalVisits = 0;
        
        for (Website website : allWebsites) {
            totalVisits += website.getVisitCount();
            
            switch (website.getStatus()) {
                case 1:
                    activeCount++;
                    break;
                case 2:
                    maintenanceCount++;
                    break;
                case 0:
                    disabledCount++;
                    break;
            }
        }
        
        return new WebsiteStats(totalCount, activeCount, maintenanceCount, disabledCount, totalVisits);
    }
    
    /**
     * 网站统计信息内部类
     */
    public static class WebsiteStats {
        private int totalCount;
        private int activeCount;
        private int maintenanceCount;
        private int disabledCount;
        private int totalVisits;
        
        public WebsiteStats(int totalCount, int activeCount, int maintenanceCount, int disabledCount, int totalVisits) {
            this.totalCount = totalCount;
            this.activeCount = activeCount;
            this.maintenanceCount = maintenanceCount;
            this.disabledCount = disabledCount;
            this.totalVisits = totalVisits;
        }
        
        // Getters
        public int getTotalCount() { return totalCount; }
        public int getActiveCount() { return activeCount; }
        public int getMaintenanceCount() { return maintenanceCount; }
        public int getDisabledCount() { return disabledCount; }
        public int getTotalVisits() { return totalVisits; }
    }
}
