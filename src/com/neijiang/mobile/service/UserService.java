package com.neijiang.mobile.service;

import com.neijiang.mobile.dao.UserDao;
import com.neijiang.mobile.model.User;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.List;

/**
 * 用户业务逻辑层
 */
public class UserService {
    private UserDao userDao;
    
    public UserService() {
        this.userDao = new UserDao();
    }
    
    /**
     * 用户注册
     */
    public boolean register(String username, String password, String realName, String email, String phone) {
        // 检查用户名是否已存在
        if (userDao.getUserByUsername(username) != null) {
            return false; // 用户名已存在
        }
        
        // 密码加密
        String encryptedPassword = encryptPassword(password);
        
        // 创建用户对象
        User user = new User(username, encryptedPassword, realName, email, phone);
        user.setRoleId(2); // 默认普通用户角色
        
        return userDao.addUser(user);
    }
    
    /**
     * 用户登录
     */
    public User login(String username, String password) {
        String encryptedPassword = encryptPassword(password);
        return userDao.login(username, encryptedPassword);
    }
    
    /**
     * 添加用户
     */
    public boolean addUser(User user) {
        // 检查用户名是否已存在
        if (userDao.getUserByUsername(user.getUsername()) != null) {
            return false;
        }
        
        // 密码加密
        user.setPassword(encryptPassword(user.getPassword()));
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        
        return userDao.addUser(user);
    }
    
    /**
     * 删除用户
     */
    public boolean deleteUser(int userId) {
        return userDao.deleteUser(userId);
    }
    
    /**
     * 更新用户信息
     */
    public boolean updateUser(User user) {
        user.setUpdateTime(new Date());
        
        // 如果密码不为空，则加密
        if (user.getPassword() != null && !user.getPassword().trim().isEmpty()) {
            user.setPassword(encryptPassword(user.getPassword()));
        } else {
            // 保持原密码
            User existingUser = userDao.getUserById(user.getUserId());
            if (existingUser != null) {
                user.setPassword(existingUser.getPassword());
            }
        }
        
        return userDao.updateUser(user);
    }
    
    /**
     * 根据ID查询用户
     */
    public User getUserById(int userId) {
        return userDao.getUserById(userId);
    }
    
    /**
     * 根据用户名查询用户
     */
    public User getUserByUsername(String username) {
        return userDao.getUserByUsername(username);
    }
    
    /**
     * 查询所有用户
     */
    public List<User> getAllUsers() {
        return userDao.getAllUsers();
    }
    
    /**
     * 修改密码
     */
    public boolean changePassword(int userId, String oldPassword, String newPassword) {
        User user = userDao.getUserById(userId);
        if (user == null) {
            return false;
        }
        
        // 验证旧密码
        String encryptedOldPassword = encryptPassword(oldPassword);
        if (!user.getPassword().equals(encryptedOldPassword)) {
            return false;
        }
        
        // 更新新密码
        user.setPassword(encryptPassword(newPassword));
        user.setUpdateTime(new Date());
        
        return userDao.updateUser(user);
    }
    
    /**
     * 启用/禁用用户
     */
    public boolean toggleUserStatus(int userId) {
        User user = userDao.getUserById(userId);
        if (user == null) {
            return false;
        }
        
        user.setStatus(user.getStatus() == 1 ? 0 : 1);
        user.setUpdateTime(new Date());
        
        return userDao.updateUser(user);
    }
    
    /**
     * 密码加密（使用MD5）
     */
    private String encryptPassword(String password) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hashBytes = md.digest(password.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return password; // 如果加密失败，返回原密码
        }
    }
    
    /**
     * 验证用户权限
     */
    public boolean hasPermission(User user, String permission) {
        if (user == null) {
            return false;
        }
        
        // 管理员拥有所有权限
        if (user.getRoleId() == 1) {
            return true;
        }
        
        // 根据角色和权限进行判断
        // 这里可以扩展更复杂的权限控制逻辑
        return user.getStatus() == 1;
    }
}
