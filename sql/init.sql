-- 内江移动支撑项目平台系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS neijiang_mobile 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE neijiang_mobile;

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(MD5加密)',
    real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    department VARCHAR(100) COMMENT '部门',
    position VARCHAR(100) COMMENT '职位',
    role_id INT DEFAULT 2 COMMENT '角色ID: 1-管理员 2-普通用户',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status INT DEFAULT 1 COMMENT '状态: 0-禁用 1-启用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 网站表
CREATE TABLE IF NOT EXISTS websites (
    website_id INT AUTO_INCREMENT PRIMARY KEY,
    site_name VARCHAR(200) NOT NULL COMMENT '网站名称',
    site_url VARCHAR(500) NOT NULL COMMENT '网站URL',
    description TEXT COMMENT '网站描述',
    category VARCHAR(100) COMMENT '网站分类',
    manager_id INT COMMENT '管理员ID',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status INT DEFAULT 1 COMMENT '状态: 0-停用 1-正常 2-维护中',
    visit_count INT DEFAULT 0 COMMENT '访问次数',
    keywords VARCHAR(500) COMMENT '关键词',
    contact VARCHAR(200) COMMENT '联系方式',
    FOREIGN KEY (manager_id) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站表';

-- 组织机构表
CREATE TABLE IF NOT EXISTS organizations (
    org_id INT AUTO_INCREMENT PRIMARY KEY,
    org_name VARCHAR(200) NOT NULL COMMENT '组织名称',
    org_code VARCHAR(50) NOT NULL UNIQUE COMMENT '组织编码',
    parent_id INT DEFAULT 0 COMMENT '父级组织ID',
    org_type VARCHAR(50) COMMENT '组织类型',
    description TEXT COMMENT '组织描述',
    manager VARCHAR(100) COMMENT '负责人',
    contact VARCHAR(200) COMMENT '联系方式',
    address VARCHAR(500) COMMENT '地址',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status INT DEFAULT 1 COMMENT '状态: 0-停用 1-正常',
    level INT DEFAULT 1 COMMENT '组织层级',
    path VARCHAR(1000) COMMENT '组织路径'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='组织机构表';

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    role_id INT AUTO_INCREMENT PRIMARY KEY,
    role_name VARCHAR(100) NOT NULL COMMENT '角色名称',
    role_code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description TEXT COMMENT '角色描述',
    permissions TEXT COMMENT '权限列表(JSON格式)',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    status INT DEFAULT 1 COMMENT '状态: 0-禁用 1-启用'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 系统日志表
CREATE TABLE IF NOT EXISTS system_logs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT COMMENT '操作用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    target_type VARCHAR(50) COMMENT '目标类型',
    target_id INT COMMENT '目标ID',
    description TEXT COMMENT '操作描述',
    ip_address VARCHAR(50) COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';

-- 插入默认角色数据
INSERT INTO roles (role_id, role_name, role_code, description, permissions) VALUES
(1, '系统管理员', 'ADMIN', '系统管理员，拥有所有权限', '["*"]'),
(2, '普通用户', 'USER', '普通用户，基本权限', '["user:view", "website:view"]');

-- 插入默认用户数据 (密码都是对应的MD5值)
INSERT INTO users (username, password, real_name, email, phone, department, position, role_id) VALUES
('admin', '0192023a7bbd73250516f069df18b500', '系统管理员', '<EMAIL>', '13800138000', 'IT部门', '系统管理员', 1),
('user', 'ee11cbb19052e40b07aac0ca060c23ee', '普通用户', '<EMAIL>', '13800138001', '业务部门', '业务员', 2);

-- 插入默认组织数据
INSERT INTO organizations (org_name, org_code, parent_id, org_type, description, manager, level, path) VALUES
('内江移动', 'NJYD', 0, '总公司', '内江移动通信有限公司', '张总', 1, '/1'),
('技术部', 'NJYD_JS', 1, '部门', '技术开发部门', '李部长', 2, '/1/2'),
('市场部', 'NJYD_SC', 1, '部门', '市场营销部门', '王部长', 2, '/1/3'),
('客服部', 'NJYD_KF', 1, '部门', '客户服务部门', '赵部长', 2, '/1/4');

-- 插入默认网站数据
INSERT INTO websites (site_name, site_url, description, category, manager_id, keywords, contact) VALUES
('12580信息服务', 'http://www.12580.com', '12580综合信息服务平台', '信息服务', 1, '12580,信息查询,生活服务', '12580'),
('内江移动官网', 'http://nj.10086.cn', '内江移动通信官方网站', '官方网站', 1, '移动,通信,套餐', '10086'),
('网上营业厅', 'http://service.10086.cn', '中国移动网上营业厅', '在线服务', 1, '营业厅,充值,查询', '10086'),
('移动商城', 'http://shop.10086.cn', '中国移动网上商城', '电商平台', 1, '手机,套餐,配件', '10086'),
('咪咕音乐', 'http://music.migu.cn', '咪咕音乐娱乐平台', '娱乐服务', 2, '音乐,娱乐,在线', '12530');

-- 创建索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_websites_category ON websites(category);
CREATE INDEX idx_websites_status ON websites(status);
CREATE INDEX idx_organizations_parent ON organizations(parent_id);
CREATE INDEX idx_organizations_code ON organizations(org_code);
CREATE INDEX idx_logs_user ON system_logs(user_id);
CREATE INDEX idx_logs_time ON system_logs(create_time);

-- 创建视图：用户详细信息视图
CREATE VIEW v_user_details AS
SELECT 
    u.user_id,
    u.username,
    u.real_name,
    u.email,
    u.phone,
    u.department,
    u.position,
    r.role_name,
    u.status,
    u.create_time,
    u.update_time
FROM users u
LEFT JOIN roles r ON u.role_id = r.role_id;

-- 创建视图：网站统计视图
CREATE VIEW v_website_stats AS
SELECT 
    category,
    COUNT(*) as total_count,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_count,
    SUM(CASE WHEN status = 2 THEN 1 ELSE 0 END) as maintenance_count,
    SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as disabled_count,
    SUM(visit_count) as total_visits,
    AVG(visit_count) as avg_visits
FROM websites
GROUP BY category;

-- 插入系统初始化日志
INSERT INTO system_logs (user_id, action, target_type, description, ip_address) VALUES
(1, 'SYSTEM_INIT', 'SYSTEM', '系统初始化完成', '127.0.0.1');

COMMIT;
