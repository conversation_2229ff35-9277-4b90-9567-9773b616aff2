# 快速启动指南

## 🚀 5分钟快速部署

### 步骤1：下载JAR包
```bash
# Windows用户
download-jars.bat

# Linux/Mac用户
chmod +x download-jars.sh
./download-jars.sh
```

### 步骤2：配置数据库
```sql
-- 1. 创建数据库
CREATE DATABASE neijiang_mobile CHARACTER SET utf8mb4;

-- 2. 导入数据
mysql -u root -p neijiang_mobile < sql/init.sql
```

### 步骤3：修改数据库配置
编辑 `src/db.properties`：
```properties
db.username=你的数据库用户名
db.password=你的数据库密码
```

### 步骤4：部署到Tomcat
1. 将整个 `web` 目录复制到 Tomcat 的 `webapps` 目录下，重命名为 `nanning`
2. 启动Tomcat服务器

### 步骤5：访问系统
- 主页：http://localhost:8080/nanning/
- 登录：admin / admin123

## 📦 必需的JAR包清单

### 核心JAR包（必须）
- ✅ mysql-connector-java-8.0.33.jar
- ✅ jstl-1.2.jar  
- ✅ standard-1.1.2.jar

### 可选JAR包（推荐）
- 🔧 commons-dbcp2-2.9.0.jar（连接池）
- 🔧 commons-pool2-2.11.1.jar（对象池）
- 🔧 commons-logging-1.2.jar（日志）

## 🔗 JAR包下载地址

### 方式1：自动下载（推荐）
运行项目根目录下的下载脚本：
- Windows: `download-jars.bat`
- Linux/Mac: `download-jars.sh`

### 方式2：手动下载
| JAR包 | 下载地址 |
|-------|----------|
| MySQL驱动 | https://repo1.maven.org/maven2/mysql/mysql-connector-java/8.0.33/mysql-connector-java-8.0.33.jar |
| JSTL | https://repo1.maven.org/maven2/javax/servlet/jstl/1.2/jstl-1.2.jar |
| Standard | https://repo1.maven.org/maven2/taglibs/standard/1.1.2/standard-1.1.2.jar |
| Commons DBCP2 | https://repo1.maven.org/maven2/org/apache/commons/commons-dbcp2/2.9.0/commons-dbcp2-2.9.0.jar |
| Commons Pool2 | https://repo1.maven.org/maven2/org/apache/commons/commons-pool2/2.11.1/commons-pool2-2.11.1.jar |
| Commons Logging | https://repo1.maven.org/maven2/commons-logging/commons-logging/1.2/commons-logging-1.2.jar |

### 方式3：Maven中央仓库
访问 https://mvnrepository.com/ 搜索对应的JAR包

## 📁 JAR包放置位置
```
web/
└── WEB-INF/
    └── lib/
        ├── mysql-connector-java-8.0.33.jar
        ├── jstl-1.2.jar
        ├── standard-1.1.2.jar
        ├── commons-dbcp2-2.9.0.jar
        ├── commons-pool2-2.11.1.jar
        └── commons-logging-1.2.jar
```

## ⚠️ 常见问题

### Q1: ClassNotFoundException: com.mysql.cj.jdbc.Driver
**解决方案：** 确保 `mysql-connector-java-8.0.33.jar` 已放置在 `web/WEB-INF/lib/` 目录

### Q2: 无法解析JSTL标签
**解决方案：** 确保 `jstl-1.2.jar` 和 `standard-1.1.2.jar` 都已添加

### Q3: 数据库连接失败
**解决方案：** 
1. 检查MySQL服务是否启动
2. 确认数据库用户名密码正确
3. 确认数据库 `neijiang_mobile` 已创建

### Q4: 页面显示404错误
**解决方案：**
1. 确认项目已正确部署到Tomcat
2. 检查访问URL是否正确
3. 确认Tomcat已启动

### Q5: 中文乱码
**解决方案：** 项目已配置UTF-8编码，如仍有问题请检查：
1. 数据库字符集是否为utf8mb4
2. Tomcat server.xml中URIEncoding配置

## 🔧 开发环境配置

### IntelliJ IDEA配置
1. 打开项目
2. File → Project Structure → Libraries
3. 添加JAR包到项目依赖
4. 配置Tomcat服务器
5. 设置部署路径为 `/nanning`

### Eclipse配置
1. 导入项目
2. 右键项目 → Properties → Java Build Path → Libraries
3. Add External JARs 添加所需JAR包
4. 配置Tomcat服务器

## 📊 系统功能验证

### 登录测试
- 管理员：admin / admin123
- 普通用户：user / user123

### 功能测试清单
- [ ] 用户登录/注销
- [ ] 用户管理（增删改查）
- [ ] 网站管理（增删改查）
- [ ] 网站分类浏览
- [ ] 权限控制
- [ ] 数据统计

## 🎯 下一步

1. **自定义配置**：根据实际需求修改数据库配置
2. **数据初始化**：添加真实的业务数据
3. **功能扩展**：基于现有框架添加新功能
4. **性能优化**：启用连接池和缓存
5. **安全加固**：配置HTTPS和更强的密码策略

## 📞 技术支持

如果遇到问题，请：
1. 查看 `DEPENDENCIES.md` 了解详细的JAR包信息
2. 查看 `DEPLOYMENT.md` 了解完整部署流程
3. 查看 `README.md` 了解项目详细信息

---

**祝您使用愉快！** 🎉
